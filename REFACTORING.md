# Bot Refactoring Documentation

## Overview

The bot code has been refactored into a modular structure for better maintainability, readability, and separation of concerns.

## File Structure

```
src/
├── bot.ts                     # Main bot instance and handler registration
├── types/
│   └── session.ts            # TypeScript type definitions
├── handlers/
│   ├── commands.ts           # Command handlers (/start, /help)
│   ├── buttons.ts            # Button text handlers
│   ├── callbacks.ts          # Callback query handlers
│   └── messages.ts           # Message handlers (echo mode, order completion)
├── services/
│   └── session.ts            # Session management service
└── utils/
    ├── keyboards.ts          # Keyboard markup utilities
    └── messageLogger.ts      # Message logging utilities
```

## Modules Description

### `bot.ts`

The main entry point that:

- Creates the Telegraf bot instance
- Registers all handlers from other modules
- Sets up error handling
- Exports the bot instance

### `types/session.ts`

Contains TypeScript type definitions for:

- `UserSession` interface
- `UserSessions` type

### `handlers/commands.ts`

Handles bot commands:

- `/start` command
- `/help` command

### `handlers/buttons.ts`

Handles button text messages:

- "👋 Hello World"
- "📋 Get My Orders"
- "✅ Complete Order"
- "🎁 Echo Gift"
- "🔗 Get Referral Link"

### `handlers/callbacks.ts`

Handles callback queries (inline button presses):

- Order help
- Contact support
- Open marketplace
- Cancel echo mode
- Order selection
- Back to orders
- Order completion
- Back to menu

### `handlers/messages.ts`

Handles incoming messages:

- Echo mode functionality
- Order completion processing
- Message logging and forwarding

### `services/session.ts`

Provides session management functionality:

- `setUserSession()` - Set user session data
- `getUserSession()` - Get user session data
- `clearUserSession()` - Clear entire user session
- `updateUserSession()` - Update specific session properties
- `clearSessionProperty()` - Clear specific session property

### `utils/keyboards.ts`

Contains reusable keyboard markup functions:

- Main keyboard
- Inline keyboards for various scenarios
- Centralized WEB_APP_URL management

### `utils/messageLogger.ts`

Provides message logging utilities:

- `getMessageInfo()` - Extract message type and details
- `logMessageDetails()` - Log message information to console

## Benefits of Refactoring

1. **Separation of Concerns**: Each file has a specific responsibility
2. **Reusability**: Common functionality is extracted into utilities
3. **Maintainability**: Easier to find and modify specific functionality
4. **Type Safety**: Better TypeScript support with defined types
5. **Testing**: Smaller modules are easier to unit test
6. **Readability**: Code is organized logically and easier to understand
7. **Scalability**: Easy to add new handlers or modify existing ones

## Usage

The bot functionality remains exactly the same as before. The refactoring is purely structural and doesn't change the user experience. All handlers work the same way:

1. **Echo Gift Mode**: Users can activate echo mode and send any type of message to get it echoed back with detailed logging
2. **Order Management**: Users can view and complete orders through the bot
3. **Referral Links**: Users can generate and share referral links
4. **Support**: Users can access help and support information

## Adding New Features

To add new functionality:

1. **New Command**: Add to `handlers/commands.ts` and register in `bot.ts`
2. **New Button**: Add to `handlers/buttons.ts` and register in `bot.ts`
3. **New Callback**: Add to `handlers/callbacks.ts` and register in `bot.ts`
4. **New Keyboard**: Add to `utils/keyboards.ts`
5. **New Session Data**: Update types in `types/session.ts`

## Dependencies

The refactored code maintains all existing dependencies:

- `telegraf` - Telegram bot framework
- `dotenv` - Environment variable management
- `axios` - HTTP client (in firebase-service.ts)

No new dependencies were added during the refactoring process.
