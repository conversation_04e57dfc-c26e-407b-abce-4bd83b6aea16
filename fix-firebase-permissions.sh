#!/bin/bash

# Firebase Functions IAM Permissions Fix Script
# This script fixes the "Permission 'iam.serviceAccounts.signBlob' denied" error
# by granting the necessary IAM roles to the Firebase Functions service account

set -e  # Exit on any error

echo "🔧 Firebase Functions IAM Permissions Fix"
echo "=========================================="

# Get project ID from .firebaserc or prompt user
if [ -f ".firebaserc" ]; then
    PROJECT_ID=$(cat .firebaserc | grep '"default"' | sed 's/.*"default": *"\([^"]*\)".*/\1/')
    echo "📋 Found project ID in .firebaserc: $PROJECT_ID"
else
    echo "❓ .firebaserc not found"
    read -p "Enter your Firebase project ID: " PROJECT_ID
fi

if [ -z "$PROJECT_ID" ]; then
    echo "❌ Error: Project ID is required"
    exit 1
fi

echo ""
echo "🚀 Granting IAM permissions for project: $PROJECT_ID"
echo "Service Account: $<EMAIL>"
echo ""

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ Error: gcloud CLI is not installed"
    echo "Please install it from: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null; then
    echo "❌ Error: Not authenticated with gcloud"
    echo "Please run: gcloud auth login"
    exit 1
fi

echo "✅ gcloud CLI is ready"

# Grant Service Account Token Creator role
echo "🔑 Granting Service Account Token Creator role..."
if gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$<EMAIL>" \
    --role="roles/iam.serviceAccountTokenCreator" \
    --quiet; then
    echo "✅ Service Account Token Creator role granted"
else
    echo "❌ Failed to grant Service Account Token Creator role"
    exit 1
fi

# Grant Service Account User role
echo "👤 Granting Service Account User role..."
if gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$<EMAIL>" \
    --role="roles/iam.serviceAccountUser" \
    --quiet; then
    echo "✅ Service Account User role granted"
else
    echo "❌ Failed to grant Service Account User role"
    exit 1
fi

echo ""
echo "🎉 Permissions granted successfully!"
echo ""
echo "📝 Next steps:"
echo "1. Build your functions: cd functions && npm run build"
echo "2. Deploy your functions: firebase deploy --only functions"
echo "3. Test the Telegram authentication"
echo ""
echo "🔍 To verify permissions were applied, run:"
echo "gcloud projects get-iam-policy $PROJECT_ID --flatten=\"bindings[].members\" --format=\"table(bindings.role)\" --filter=\"bindings.members:$<EMAIL>\""
