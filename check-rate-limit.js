const { Telegraf } = require('telegraf');
require('dotenv').config();

const bot = new Telegraf(process.env.BOT_TOKEN);

async function checkRateLimit() {
  try {
    console.log('🔍 Checking if rate limit has expired...');
    const botInfo = await bot.telegram.getMe();
    console.log(`✅ Rate limit has expired! Bot @${botInfo.username} is accessible`);
    console.log('🚀 You can now start your bot again');
    process.exit(0);
  } catch (error) {
    if (error.response?.error_code === 429) {
      const retryAfter = error.response.parameters?.retry_after ?? 60;
      console.log(`⏳ Still rate limited. Retry after ${retryAfter} seconds`);
      console.log(`🕐 Current time: ${new Date().toLocaleTimeString()}`);
      console.log(`🕐 Try again at: ${new Date(Date.now() + retryAfter * 1000).toLocaleTimeString()}`);
    } else {
      console.error('❌ Error:', error.message);
    }
    process.exit(1);
  }
}

checkRateLimit();
