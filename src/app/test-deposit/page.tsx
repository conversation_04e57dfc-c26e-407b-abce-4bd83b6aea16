"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { DepositDrawer } from '@/components/DepositDrawer';
import { setupAppConfig } from '@/utils/setup-app-config';
import { useRootContext } from '@/root-context';
import { toast } from 'sonner';

export default function TestDeposit() {
  const { appConfig } = useRootContext();
  const [showDepositDrawer, setShowDepositDrawer] = useState(false);

  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Deposit Functionality</h1>
      
      <div className="space-y-4">
        <Button 
          onClick={() => setShowDepositDrawer(true)}
          className="w-full bg-ton-main hover:bg-ton-main/90"
        >
          Test Deposit Drawer
        </Button>
      </div>

      {appConfig && (
        <div className="mt-6 p-4 bg-gray-100 rounded-lg">
          <h3 className="font-semibold mb-2">Current Config:</h3>
          <pre className="text-sm">{JSON.stringify(appConfig, null, 2)}</pre>
        </div>
      )}

      <DepositDrawer 
        open={showDepositDrawer} 
        onOpenChange={setShowDepositDrawer} 
      />
    </div>
  );
}
