"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { DepositDrawer } from '@/components/DepositDrawer';
import { setupAppConfig, updateAppConfig } from '@/utils/setup-app-config';
import { useRootContext } from '@/root-context';
import { toast } from 'sonner';
import { Plus } from 'lucide-react';

export default function TestDepositFlow() {
  const { appConfig } = useRootContext();
  const [showDepositDrawer, setShowDepositDrawer] = useState(false);

  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Complete Deposit Flow</h1>
      
      <div className="space-y-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm">
          <h3 className="font-semibold text-blue-800 mb-2">Test Flow:</h3>
          <ol className="list-decimal pl-5 space-y-1 text-blue-700">
            <li>Setup or load app config</li>
            <li>Click the Plus button to open deposit drawer</li>
            <li>Enter deposit amount and click Deposit</li>
            <li>After transaction, countdown popup appears</li>
            <li>Wait 60 seconds or close manually</li>
            <li>User balance gets refetched automatically</li>
          </ol>
        </div>
      
        
        <div className="flex items-center justify-center">
          <Button 
            onClick={() => setShowDepositDrawer(true)}
            className="flex items-center gap-2 bg-ton-main hover:bg-ton-main/90"
          >
            <Plus className="w-4 h-4" />
            4. Test Deposit Flow
          </Button>
        </div>
      </div>

      {appConfig && (
        <div className="mt-6 p-4 bg-gray-100 rounded-lg">
          <h3 className="font-semibold mb-2">Current Config:</h3>
          <div className="text-sm space-y-1">
            <div>Min Deposit: <strong>{appConfig.minDepositAmount} TON</strong></div>
            <div>Deposit Fee: <strong>{appConfig.depositFee} TON</strong></div>
            <div>Withdrawal Fee: <strong>{appConfig.withdrawalFee} TON</strong></div>
            <div>Max Deposit: <strong>{appConfig.maxDepositAmount} TON</strong></div>
            <div>Maintenance: <strong>{appConfig.maintenanceMode ? 'Yes' : 'No'}</strong></div>
          </div>
        </div>
      )}

      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-sm">
        <h3 className="font-semibold text-yellow-800 mb-2">Environment Info:</h3>
        <div className="text-yellow-700 space-y-1">
          <div>Marketplace Wallet: <code className="text-xs">{process.env.NEXT_PUBLIC_MARKETPLACE_WALLET_ADDRESS}</code></div>
          <div>Server: <code>http://*************:3001</code></div>
        </div>
      </div>

      <DepositDrawer 
        open={showDepositDrawer} 
        onOpenChange={setShowDepositDrawer} 
      />
    </div>
  );
}
