"use client";

import { loadFeesConfig } from "@/api/fees-api";
import {
  getUserById,
  getUsersWithCustomReferralFees,
  updateUser,
} from "@/api/user-api";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus, Trash2, Users } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const customReferralSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  referralFee: z.number().min(0, "Referral fee must be non-negative"),
});

type CustomReferralFormData = z.infer<typeof customReferralSchema>;

interface UserWithCustomReferral {
  id: string;
  displayName?: string | null;
  email?: string | null;
  tg_id?: string;
  referral_fee: number;
}

export const CustomReferralManagement = () => {
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [usersWithCustomReferrals, setUsersWithCustomReferrals] = useState<
    UserWithCustomReferral[]
  >([]);
  const [maxPurchaseFee, setMaxPurchaseFee] = useState<number>(0);

  const form = useForm<CustomReferralFormData>({
    resolver: zodResolver(customReferralSchema),
    defaultValues: {
      userId: "",
      referralFee: 0,
    },
  });

  const loadMaxPurchaseFee = async () => {
    try {
      const config = await loadFeesConfig();
      setMaxPurchaseFee(config?.purchase_fee || 0);
    } catch (error) {
      console.error("Error loading purchase fee:", error);
    }
  };

  const loadUsersWithCustomReferrals = async () => {
    try {
      const users = await getUsersWithCustomReferralFees();
      const usersWithCustomReferrals = users.map((user) => ({
        id: user.id,
        displayName: user.displayName,
        email: user.email,
        tg_id: user.tg_id,
        referral_fee: user.referral_fee || 0,
      }));
      setUsersWithCustomReferrals(usersWithCustomReferrals);
    } catch (error) {
      console.error("Error loading users with custom referral fees:", error);
    }
  };

  useEffect(() => {
    loadMaxPurchaseFee();
    loadUsersWithCustomReferrals();
  }, []);

  const onSubmit = async (data: CustomReferralFormData) => {
    if (data.referralFee > maxPurchaseFee) {
      toast({
        title: "Error",
        description: `Referral fee cannot be more than purchase fee (${maxPurchaseFee} BPS)`,
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      // First, check if user exists
      const user = await getUserById(data.userId);
      if (!user) {
        toast({
          title: "Error",
          description: "User not found",
          variant: "destructive",
        });
        return;
      }

      // Update user with custom referral fee
      await updateUser(data.userId, {
        referral_fee: data.referralFee,
      });

      // Refresh the list to show the updated user
      await loadUsersWithCustomReferrals();

      toast({
        title: "Success",
        description: "Custom referral fee set successfully",
      });

      form.reset();
      setIsDialogOpen(false);
    } catch (error) {
      console.error("Error setting custom referral fee:", error);
      toast({
        title: "Error",
        description: "Failed to set custom referral fee",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const removeCustomReferralFee = async (userId: string) => {
    try {
      await updateUser(userId, {
        referral_fee: undefined,
      });

      // Refresh the list to show the updated state
      await loadUsersWithCustomReferrals();

      toast({
        title: "Success",
        description: "Custom referral fee removed successfully",
      });
    } catch (error) {
      console.error("Error removing custom referral fee:", error);
      toast({
        title: "Error",
        description: "Failed to remove custom referral fee",
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Users className="h-4 w-4" />
          Custom Referral Fees
        </CardTitle>
        <CardDescription className="text-sm">
          Set custom referral fees for specific users
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              Max allowed: {maxPurchaseFee} BPS (Purchase Fee)
            </p>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Custom Fee
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Set Custom Referral Fee</DialogTitle>
                  <DialogDescription>
                    Set a custom referral fee for a specific user. The fee
                    cannot exceed the purchase fee ({maxPurchaseFee} BPS).
                  </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-4"
                  >
                    <FormField
                      control={form.control}
                      name="userId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>User ID</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter user ID" {...field} />
                          </FormControl>
                          <FormDescription>
                            The Firebase user ID of the user
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="referralFee"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Referral Fee (BPS)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="0"
                              {...field}
                              onChange={(e) =>
                                field.onChange(Number(e.target.value))
                              }
                            />
                          </FormControl>
                          <FormDescription>
                            Referral fee in basis points (100 BPS = 1%). Max:{" "}
                            {maxPurchaseFee} BPS
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <DialogFooter>
                      <Button type="submit" disabled={isLoading}>
                        {isLoading ? "Setting..." : "Set Custom Fee"}
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>

          {usersWithCustomReferrals.length > 0 ? (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User ID</TableHead>
                    <TableHead>Display Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Telegram ID</TableHead>
                    <TableHead>Custom Fee (BPS)</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {usersWithCustomReferrals.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-mono text-xs">
                        {user.id}
                      </TableCell>
                      <TableCell>{user.displayName || "N/A"}</TableCell>
                      <TableCell>{user.email || "N/A"}</TableCell>
                      <TableCell>{user.tg_id || "N/A"}</TableCell>
                      <TableCell className="font-semibold">
                        {user.referral_fee}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => removeCustomReferralFee(user.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No users with custom referral fees</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
