"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CountdownPopup } from "@/components/CountdownPopup";
import { toast } from "sonner";

export default function TestCountdown() {
  const [showPopup, setShowPopup] = useState(false);

  const handleComplete = () => {
    toast.success("Countdown completed! User balance would be refetched here.");
    setShowPopup(false);
  };

  const handleClose = () => {
    setShowPopup(false);
  };

  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Countdown Popup</h1>

      <div className="space-y-4">
        <Button
          onClick={() => setShowPopup(true)}
          className="w-full bg-ton-main hover:bg-ton-main/90"
        >
          Show Countdown Popup (60s)
        </Button>

        <Button
          onClick={() => setShowPopup(false)}
          variant="outline"
          className="w-full"
        >
          Hide Popup
        </Button>
      </div>

      <div className="mt-6 p-4 bg-gray-100 rounded-lg text-sm">
        <h3 className="font-semibold mb-2">Test Instructions:</h3>
        <ul className="list-disc pl-5 space-y-1">
          <li>
            Click &quot;Show Countdown Popup&quot; to start a 60-second timer
          </li>
          <li>The popup will show at the bottom with a countdown</li>
          <li>You can close it manually with the X button</li>
          <li>Or wait for it to complete automatically</li>
          <li>When complete, it will show a success toast</li>
        </ul>
      </div>

      <CountdownPopup
        show={showPopup}
        onClose={handleClose}
        onComplete={handleComplete}
        initialSeconds={60}
        title="Test Deposit Processing"
        message="Test funds will be received within"
      />
    </div>
  );
}
