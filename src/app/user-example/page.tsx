"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { getUserById } from "@/api/auth-api";
import { UserEntity } from "@/core.constants";
import UserProfileDisplay from "@/components/user-profile-display";

export default function UserExamplePage() {
  const [userId, setUserId] = useState("");
  const [user, setUser] = useState<UserEntity | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const handleGetUser = async () => {
    try {
      setIsLoading(true);
      setError("");

      // Call getUserById with the provided userId (or current user if empty)
      const userData = await getUserById(userId || undefined);
      setUser(userData);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch user";
      setError(errorMessage);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGetCurrentUser = async () => {
    try {
      setIsLoading(true);
      setError("");

      // Call getUserById without parameters to get current user
      const userData = await getUserById();
      setUser(userData);
      setUserId(""); // Clear the input
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch current user";
      setError(errorMessage);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          User Data Example - getUserById Method
        </h1>

        {/* Controls */}
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">
            Test getUserById Method
          </h2>

          <div className="space-y-4">
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <label
                  htmlFor="userId"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  User ID (leave empty for current user)
                </label>
                <Input
                  id="userId"
                  type="text"
                  placeholder="Enter user ID or leave empty for current user"
                  value={userId}
                  onChange={(e) => setUserId(e.target.value)}
                  className="w-full"
                />
              </div>
              <Button
                onClick={handleGetUser}
                disabled={isLoading}
                className="px-6"
              >
                {isLoading ? "Loading..." : "Get User"}
              </Button>
            </div>

            <div className="flex gap-4">
              <Button
                onClick={handleGetCurrentUser}
                disabled={isLoading}
                variant="outline"
              >
                Get Current User
              </Button>
            </div>
          </div>

          {error && (
            <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-600 text-sm">Error: {error}</p>
            </div>
          )}
        </div>

        {/* Results */}
        {user && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">User Data Result</h2>

            {/* Using the UserProfileDisplay component */}
            <UserProfileDisplay userId={user.id} />

            {/* Raw JSON data */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-medium mb-3">Raw User Data (JSON)</h3>
              <pre className="text-sm text-gray-700 overflow-x-auto">
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Documentation */}
        <div className="mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-blue-900 mb-4">
            getUserById Method Documentation
          </h2>

          <div className="space-y-4 text-blue-800">
            <div>
              <h3 className="font-semibold">Function Signature:</h3>
              <code className="bg-blue-100 px-2 py-1 rounded text-sm">
                getUserById(userId?: string): Promise&lt;UserEntity | null&gt;
              </code>
            </div>

            <div>
              <h3 className="font-semibold">Parameters:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>
                  <strong>userId</strong> (optional): The ID of the user to
                  fetch. If not provided, fetches the current authenticated
                  user.
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold">Returns:</h3>
              <p className="text-sm">
                A Promise that resolves to a UserEntity object or null if the
                user is not found.
              </p>
            </div>

            <div>
              <h3 className="font-semibold">Usage Examples:</h3>
              <div className="bg-blue-100 p-3 rounded text-sm font-mono space-y-2">
                <div>Get current user</div>
                <div>const currentUser = await getUserById();</div>
                <div></div>
                <div>Get specific user by ID</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
