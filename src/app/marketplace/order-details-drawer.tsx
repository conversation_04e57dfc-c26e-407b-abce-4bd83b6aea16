"use client";

import { useState, useEffect } from 'react';
import { Drawer } from 'vaul';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, User, ShoppingCart, Package } from 'lucide-react';
import { useRootContext } from '@/root-context';
import { OrderEntity, UserEntity } from '@/core.constants';
import { getUserById } from '@/api/auth-api';
import { toast } from 'sonner';
import { httpsCallable } from 'firebase/functions';
import { firebaseFunctions } from '@/root-context';
import Image from 'next/image';

interface OrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  orderType: 'seller' | 'buyer'; // Which tab we're on
  onOrderAction?: () => void; // Callback after successful action
}

export function OrderDetailsDrawer({ 
  open, 
  onOpenChange, 
  order, 
  orderType,
  onOrderAction 
}: OrderDetailsDrawerProps) {
  const { collections } = useRootContext();
  const [userInfo, setUserInfo] = useState<UserEntity | null>(null);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [imageError, setImageError] = useState(false);

  const collection = order ? collections.find(c => c.id === order.collectionId) : null;
  
  // Determine which user to fetch based on order type
  const userIdToFetch = orderType === 'seller' ? order?.buyerId : order?.sellerId;

  useEffect(() => {
    if (open && userIdToFetch) {
      loadUserInfo();
    }
  }, [open, userIdToFetch]);

  const loadUserInfo = async () => {
    if (!userIdToFetch) return;
    
    setLoading(true);
    try {
      const user = await getUserById(userIdToFetch);
      setUserInfo(user);
    } catch (error) {
      console.error('Error loading user info:', error);
      setUserInfo(null);
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async () => {
    if (!order) return;

    setActionLoading(true);
    try {
      const functionName = orderType === 'buyer' ? 'makePurchase' : 'completePurchase';
      const actionFunction = httpsCallable(firebaseFunctions, functionName);
      
      const result = await actionFunction({
        orderId: order.id
      });

      toast.success(result.data.message || 'Action completed successfully!');
      onOpenChange(false);
      
      if (onOrderAction) {
        onOrderAction();
      }
      
    } catch (error: any) {
      console.error('Action failed:', error);
      const errorMessage = error.message || 'Action failed. Please try again.';
      toast.error(errorMessage);
    } finally {
      setActionLoading(false);
    }
  };

  const getImageSrc = () => {
    if (!order?.collectionId) return '';
    // Use PNG by default to avoid TGS 404 errors
    return `/limited/${order.collectionId}/Original.png`;
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const handleClose = () => {
    setUserInfo(null);
    setImageError(false);
    onOpenChange(false);
  };

  if (!order) return null;

  return (
    <Drawer.Root open={open} onOpenChange={onOpenChange}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-50" />
        <Drawer.Content className="bg-[#232e3c] flex flex-col rounded-t-[10px] h-fit mt-24 max-h-[80vh] fixed bottom-0 left-0 right-0 z-50 border-t border-[#3a4a5c]">
          <div className="p-4 bg-[#232e3c] rounded-t-[10px] flex-1 overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-8" />

            <div className="max-w-md mx-auto space-y-6">
              <Drawer.Title className="font-medium text-lg flex items-center gap-2 text-[#f5f5f5]">
                <Package className="w-5 h-5 text-[#6ab2f2]" />
                Order Details
              </Drawer.Title>

              {/* Order Image */}
              <div className="flex justify-center">
                <div className="w-32 h-32 relative rounded-lg overflow-hidden bg-[#17212b]">
                  <Image
                    src={getImageSrc()}
                    alt={collection?.name || 'Order item'}
                    fill
                    className="object-cover"
                    onError={handleImageError}
                  />
                </div>
              </div>

              {/* Collection Info */}
              <div className="space-y-3">
                <div>
                  <h3 className="font-semibold text-lg text-[#f5f5f5]">{collection?.name || 'Unknown Collection'}</h3>
                  {collection?.description && (
                    <p className="text-[#708499] text-sm mt-1">{collection.description}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-[#708499]">Floor Price</span>
                    <p className="font-medium text-[#f5f5f5]">{collection?.floorPrice || 'N/A'} TON</p>
                  </div>
                  <div>
                    <span className="text-[#708499]">Order Price</span>
                    <p className="font-medium text-[#6ab2f2]">{order.amount} TON</p>
                  </div>
                </div>

                <div>
                  <span className="text-[#708499] text-sm">Status</span>
                  <div className="mt-1">
                    <Badge variant="secondary">{collection?.status}</Badge>
                  </div>
                </div>
              </div>

              {/* User Info */}
              <div className="border-t border-[#3a4a5c] pt-4">
                <div className="flex items-center gap-2 mb-3">
                  <User className="w-4 h-4 text-[#708499]" />
                  <span className="text-sm font-medium text-[#f5f5f5]">
                    {orderType === 'seller' ? 'Buyer Information' : 'Seller Information'}
                  </span>
                </div>

                {loading ? (
                  <div className="flex items-center gap-2 text-[#708499]">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm">Loading user info...</span>
                  </div>
                ) : userInfo ? (
                  <div className="bg-[#17212b] border border-[#3a4a5c] rounded-lg p-3">
                    <p className="font-medium text-[#f5f5f5]">{userInfo.displayName || userInfo.email || 'Anonymous User'}</p>
                    {userInfo.email && userInfo.displayName && (
                      <p className="text-sm text-[#708499]">{userInfo.email}</p>
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-[#708499]">User information not available</p>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={handleClose}
                  className="flex-1 border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#17212b]"
                  disabled={actionLoading}
                >
                  Close
                </Button>
                <Button
                  onClick={handleAction}
                  disabled={actionLoading}
                  className="flex-1 bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white"
                >
                  {actionLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="w-4 h-4 mr-2" />
                      {orderType === 'buyer' ? 'Buy' : 'Fulfill'}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
