"use client";

import { useState } from 'react';
import MarketplaceHeader from "@/app/marketplace/marketplace-header";
import MarketplaceFooter from "@/app/marketplace/marketplace-footer";
import OrdersPage from "@/app/marketplace/orders-page";
import BackyardLottie from "@/components/BackyardLottie";

export default function MarketplacePage() {
  const [activeTab, setActiveTab] = useState<string>('market');

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'market':
        return <OrdersPage />;
      case 'orders':
      default:
        return (
          <div className="text-center py-20 bg-[#17212b]">
            <div className="mb-8 flex justify-center">
              <BackyardLottie width={300} height={300} className="rounded-lg" />
            </div>
            <h2 className="text-2xl font-bold text-[#f5f5f5] mb-4">
              No gifts found with the filters you selected
            </h2>
            <p className="text-[#708499]">
              Try adjusting your filters or browse all available items.
            </p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-scree flex flex-col">
      <MarketplaceHeader />

      <main className="flex-1 p-2">
        <div className="max-w-6xl mx-auto">
          {renderContent()}
        </div>
      </main>

      <MarketplaceFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  );
}
