"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  TrendingUp,
  Hammer,
  Gift,
  Building,
  Camera,
  Activity,
  ShoppingCart,
} from "lucide-react";

interface MarketplaceFooterProps {
  activeTab?: string;
  onTabChange?: (tab: string) => void;
}

export default function MarketplaceFooter({ activeTab = "market", onTabChange }: MarketplaceFooterProps) {
  const navItems = [
    {
      icon: TrendingUp,
      label: "Market",
      key: "market",
      active: activeTab === "market",
    },
    {
      icon: ShoppingCart,
      label: "Orders",
      key: "orders",
      active: activeTab === "orders",
    },
    {
      icon: Hammer,
      label: "Auctions",
      key: "auctions",
      active: false,
    },
    {
      icon: Gift,
      label: "My Gifts",
      key: "gifts",
      active: false,
    },
    {
      icon: Building,
      label: "GiFi",
      key: "gifi",
      active: false,
    },
    {
      icon: Camera,
      label: "Gallery",
      key: "gallery",
      active: false,
    },
    {
      icon: Activity,
      label: "Activity",
      key: "activity",
      active: false,
    },
  ];

  const handleTabClick = (key: string) => {
    if (onTabChange && (key === "market" || key === "orders")) {
      onTabChange(key);
    }
  };

  return (
    <footer className="bg-[#17212b] text-[#f5f5f5] border-t border-[#3a4a5c]">
      <div className="flex items-center justify-around py-3">
        {navItems.map((item, index) => {
          const IconComponent = item.icon;
          return (
            <Button
              key={index}
              variant="ghost"
              onClick={() => handleTabClick(item.key)}
              className={`flex flex-col items-center gap-1 p-2 h-auto ${
                item.active ? "text-[#6ab2f2]" : "text-[#708499] hover:text-[#f5f5f5]"
              }`}
            >
              <IconComponent className="w-6 h-6" />
              <span className="text-xs font-medium">{item.label}</span>
            </Button>
          );
        })}
      </div>
    </footer>
  );
}
