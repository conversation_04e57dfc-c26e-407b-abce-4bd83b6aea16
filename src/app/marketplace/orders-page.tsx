"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useCallback, useEffect, useRef, useState } from "react";

import { getOrdersForBuyers, getOrdersForSellers } from "@/api/order-api";
import { CollectionSelect } from "@/components/ui/collection-select";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { OrderEntity } from "@/core.constants";
import { useRootContext } from "@/root-context";
import { Select, Spinner, TabsList, Input as TgInput } from "@telegram-apps/telegram-ui";
import { TabsItem } from "@telegram-apps/telegram-ui/dist/components/Navigation/TabsList/components/TabsItem/TabsItem";
import { Loader2, Plus } from "lucide-react";
import { CreateOrderDrawer } from "./create-order-drawer";
import { OrderCard } from "./order-card";
import { OrderDetailsDrawer } from "./order-details-drawer";

interface OrdersPageProps {}

export default function OrdersPage({}: OrdersPageProps) {
  const { collections } = useRootContext();
  const [activeTab, setActiveTab] = useState<"sellers" | "buyers">("sellers");
  const [showCreateOrderDrawer, setShowCreateOrderDrawer] = useState(false);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);
  // Separate state for each tab to prevent duplicate keys and improve UX
  const [sellersOrders, setSellersOrders] = useState<OrderEntity[]>([]);
  const [buyersOrders, setBuyersOrders] = useState<OrderEntity[]>([]);
  const [sellersLoading, setSellersLoading] = useState(false);
  const [buyersLoading, setBuyersLoading] = useState(false);
  const [sellersLoadingMore, setSellersLoadingMore] = useState(false);
  const [buyersLoadingMore, setBuyersLoadingMore] = useState(false);
  const [sellersHasMore, setSellersHasMore] = useState(true);
  const [buyersHasMore, setBuyersHasMore] = useState(true);
  const [sellersLastDoc, setSellersLastDoc] = useState<any>(null);
  const [buyersLastDoc, setBuyersLastDoc] = useState<any>(null);

  // Filters
  const [minPrice, setMinPrice] = useState("");
  const [maxPrice, setMaxPrice] = useState("");
  const [selectedCollection, setSelectedCollection] = useState<string>("all");
  const [sortBy, setSortBy] = useState<
    "price_asc" | "price_desc" | "date_asc" | "date_desc"
  >("date_desc");

  // Ref for infinite scroll
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const loadOrders = useCallback(
    async (reset = true) => {
      const isSellersTab = activeTab === "sellers";

      if (reset) {
        if (isSellersTab) {
          setSellersLoading(true);
          setSellersOrders([]);
          setSellersLastDoc(null);
          setSellersHasMore(true);
        } else {
          setBuyersLoading(true);
          setBuyersOrders([]);
          setBuyersLastDoc(null);
          setBuyersHasMore(true);
        }
      } else {
        if (isSellersTab) {
          setSellersLoadingMore(true);
        } else {
          setBuyersLoadingMore(true);
        }
      }

      try {
        const currentLastDoc = isSellersTab ? sellersLastDoc : buyersLastDoc;
        // Ensure sortBy is a valid string value
        const validSortBy = typeof sortBy === 'string' &&
          ['price_asc', 'price_desc', 'date_asc', 'date_desc'].includes(sortBy)
          ? sortBy
          : 'date_desc';

        const filters = {
          minPrice: minPrice ? parseFloat(minPrice) : undefined,
          maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,
          collectionId:
            selectedCollection !== "all" ? selectedCollection : undefined,
          sortBy: validSortBy,
          limit: 3,
          lastDoc: reset ? null : currentLastDoc,
        };



        let result;
        if (isSellersTab) {
          // For sellers tab: get orders where buyerId !== null and sellerId === null
          result = await getOrdersForSellers(filters);
        } else {
          // For buyers tab: get orders where sellerId !== null and buyerId === null
          result = await getOrdersForBuyers(filters);
        }

        if (isSellersTab) {
          if (reset) {
            console.log(
              "🔄 Resetting sellers orders with",
              result.orders.length,
              "orders"
            );
            setSellersOrders(result.orders);
          } else {
            // Deduplicate orders to prevent duplicate keys
            setSellersOrders((prev) => {
              const existingIds = new Set(prev.map((order) => order.id));
              const newOrders = result.orders.filter(
                (order) => !existingIds.has(order.id)
              );
              const duplicates = result.orders.filter((order) =>
                existingIds.has(order.id)
              );

              if (duplicates.length > 0) {
                console.warn(
                  "🚨 Found duplicate orders for sellers:",
                  duplicates.map((o) => o.id)
                );
              }

              console.log(
                "➕ Adding",
                newOrders.length,
                "new sellers orders, filtered out",
                duplicates.length,
                "duplicates"
              );
              return [...prev, ...newOrders];
            });
          }
          setSellersLastDoc(result.lastDoc);
          setSellersHasMore(result.hasMore);
        } else {
          if (reset) {
            console.log(
              "🔄 Resetting buyers orders with",
              result.orders.length,
              "orders"
            );
            setBuyersOrders(result.orders);
          } else {
            // Deduplicate orders to prevent duplicate keys
            setBuyersOrders((prev) => {
              const existingIds = new Set(prev.map((order) => order.id));
              const newOrders = result.orders.filter(
                (order) => !existingIds.has(order.id)
              );
              const duplicates = result.orders.filter((order) =>
                existingIds.has(order.id)
              );

              if (duplicates.length > 0) {
                console.warn(
                  "🚨 Found duplicate orders for buyers:",
                  duplicates.map((o) => o.id)
                );
              }

              console.log(
                "➕ Adding",
                newOrders.length,
                "new buyers orders, filtered out",
                duplicates.length,
                "duplicates"
              );
              return [...prev, ...newOrders];
            });
          }
          setBuyersLastDoc(result.lastDoc);
          setBuyersHasMore(result.hasMore);
        }
      } catch (error) {
        console.error("Error loading orders:", error);
      } finally {
        if (isSellersTab) {
          setSellersLoading(false);
          setSellersLoadingMore(false);
        } else {
          setBuyersLoading(false);
          setBuyersLoadingMore(false);
        }
      }
    },
    [
      activeTab,
      minPrice,
      maxPrice,
      selectedCollection,
      sortBy,
      sellersLastDoc,
      buyersLastDoc,
    ]
  );

  const loadMore = useCallback(() => {
    loadOrders(false);
  }, [loadOrders]);

  useEffect(() => {
    loadOrders(true);
  }, [activeTab, minPrice, maxPrice, selectedCollection, sortBy]);

  // Intersection Observer for infinite scroll
  useEffect(() => {
    const isSellersTab = activeTab === "sellers";
    const hasMore = isSellersTab ? sellersHasMore : buyersHasMore;
    const loading = isSellersTab ? sellersLoading : buyersLoading;
    const loadingMore = isSellersTab ? sellersLoadingMore : buyersLoadingMore;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading && !loadingMore) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [
    loadMore,
    activeTab,
    sellersHasMore,
    buyersHasMore,
    sellersLoading,
    buyersLoading,
    sellersLoadingMore,
    buyersLoadingMore,
  ]);

  const handleCreateOrder = () => {
    setShowCreateOrderDrawer(true);
  };

  const handleOrderCreated = () => {
    loadOrders(true); // Refresh orders after creating
  };

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderAction = () => {
    loadOrders(true); // Refresh orders after action
  };

  return (
    <div className="space-y-2 bg-[#17212b] min-h-screen">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-[#f5f5f5]">Orders</h1>
        <div className="flex items-center gap-2">
          {/* <Button
            variant="outline"
            onClick={() => debugAllOrders()}
            className="text-[#f5f5f5] border-[#3a4a5c] hover:bg-[#232e3c]"
          >
            Debug Orders
          </Button> */}
          <Button
            onClick={handleCreateOrder}
            className="flex items-center gap-2 bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white"
          >
            <Plus className="w-4 h-4" />
            Create Order
          </Button>
        </div>
      </div>

      <Tabs value={activeTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsItem
            selected={activeTab === "sellers"}
            onClick={() => setActiveTab("sellers")}
          >
            For Sellers
          </TabsItem>
          <TabsItem
            selected={activeTab === "buyers"}
            onClick={() => setActiveTab("buyers")}
          >
            For Buyers
          </TabsItem>
        </TabsList>

        {/* Unified Filters - Single Row */}
        <div className="flex flex-wrap items-end gap-2 p-3 rounded-lg">
          <div className="flex-1 min-w-[100px] xss:min-w-[120px]">
            <div className="[&>div]:p-0!">
              <TgInput
                type="number"
                header="Min"
                placeholder="0"
                value={minPrice}
                onChange={(e) => {
                  const value = e.target.value;
                  // Allow empty string or valid numbers (including decimals)
                  if (value === "" || /^\d*\.?\d*$/.test(value)) {
                    setMinPrice(value);
                  }
                }}
                min="0"
                step="0.01"
                className="text-white text-sm h-9 [&+h6]:-top-[12px]!"
              />
            </div>
          </div>
          <div className="flex-1 min-w-[100px] xss:min-w-[120px]">
            <div className="[&>div]:p-0!">
              <TgInput
                type="number"
                header="Max"
                placeholder="0"
                value={maxPrice}
                onChange={(e) => {
                  const value = e.target.value;
                  // Allow empty string or valid numbers (including decimals)
                  if (value === "" || /^\d*\.?\d*$/.test(value)) {
                    setMaxPrice(value);
                  }
                }}
                min="0"
                step="0.01"
                className="text-white text-sm h-9 [&+h6]:-top-[12px]!"
              />
            </div>
          </div>

          <div className="flex-1 min-w-[120px] xss:min-w-[140px] mt-2">
            <CollectionSelect
              animated
              collections={collections}
              value={selectedCollection}
              onValueChange={setSelectedCollection}
              placeholder="All Collections"
            />
          </div>
          <div className="flex-1 min-w-[120px] xss:min-w-[140px]">
            <div className="[&>div]:p-0!">
              <Select
                header="Sort by"
                value={sortBy}
                onChange={(e) => {
                  const value = e.target.value as "price_asc" | "price_desc" | "date_asc" | "date_desc";
                  setSortBy(value);
                }}
                className="[&>select]:px-[12px]! [&>select]:py-[6px]! [&+h6]:-top-[12px]!"
              >
                <option value={"date_desc"}>Newest First</option>
                <option value={"date_asc"}>Oldest First</option>
                <option value={"price_desc"}>Price: High to Low</option>
                <option value={"price_asc"}>Price: Low to High</option>
              </Select>
            </div>
          </div>
        </div>

        <TabsContent value="sellers" className="space-y-4">
          <div className="text-sm text-[#708499]">
            Orders where buyers are looking for sellers (buyerId exists,
            sellerId needed)
          </div>
          {sellersLoading ? (
            <div className="text-center py-8">
              <Spinner className="flex justify-center" size="l" />
              <p className="text-[#708499] mt-2">Loading orders...</p>
            </div>
          ) : sellersOrders.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-[#708499]">No orders found for sellers</p>
            </div>
          ) : (
            <div className="grid grid-cols-2 xl:grid-cols-4 gap-2">
              {sellersOrders.map((order, index) => (
                <OrderCard
                  animated
                  key={`sellers-${order.id}-${index}`}
                  order={order}
                  collection={collections.find(
                    (c) => c.id === order.collectionId
                  )}
                  onClick={() => handleOrderClick(order)}
                />
              ))}
            </div>
          )}

          {/* Load more trigger for infinite scroll */}
          {sellersHasMore && (
            <div
              ref={activeTab === "sellers" ? loadMoreRef : null}
              className="flex flex-col items-center gap-2 py-4"
            >
              {sellersLoadingMore ? (
                <div className="flex items-center gap-2 text-gray-400">
                  <Spinner className="flex justify-center" size="l" />
                  <span>Loading more orders...</span>
                </div>
              ) : (
                <Button
                  variant="outline"
                  onClick={() => loadMore()}
                  className="text-white border-gray-600 hover:bg-gray-700"
                >
                  Load More Orders
                </Button>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="buyers" className="space-y-4">
          <div className="text-sm text-[#708499]">
            Orders where sellers are looking for buyers (sellerId exists,
            buyerId needed)
          </div>
          {buyersLoading ? (
            <div className="text-center py-8">
              <Spinner className="flex justify-center" size="l" />
              <p className="text-[#708499] mt-2">Loading orders...</p>
            </div>
          ) : buyersOrders.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-[#708499]">No orders found for buyers</p>
            </div>
          ) : (
            <div className="grid grid-cols-2 xl:grid-cols-4 gap-2">
              {buyersOrders.map((order, index) => (
                <OrderCard
                  animated
                  key={`buyers-${order.id}-${index}`}
                  order={order}
                  collection={collections.find(
                    (c) => c.id === order.collectionId
                  )}
                  onClick={() => handleOrderClick(order)}
                />
              ))}
            </div>
          )}

          {/* Load more trigger for infinite scroll */}
          {buyersHasMore && (
            <div
              ref={activeTab === "buyers" ? loadMoreRef : null}
              className="flex flex-col items-center gap-2 py-4"
            >
              {buyersLoadingMore ? (
                <div className="flex items-center gap-2 text-gray-400">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Loading more orders...</span>
                </div>
              ) : (
                <Button
                  variant="outline"
                  onClick={() => loadMore()}
                  className="text-white border-gray-600 hover:bg-gray-700"
                >
                  Load More Orders
                </Button>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>

      <CreateOrderDrawer
        open={showCreateOrderDrawer}
        onOpenChange={setShowCreateOrderDrawer}
        orderType={activeTab === "sellers" ? "buyer" : "seller"}
        onOrderCreated={handleOrderCreated}
      />

      <OrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        orderType={activeTab === "sellers" ? "seller" : "buyer"}
        onOrderAction={handleOrderAction}
      />
    </div>
  );
}
