import { UserSession } from "../types/session";
import { redisService } from "./redis";

const SESSION_PREFIX = "session:";
const SESSION_TTL = 24 * 60 * 60; // 24 hours in seconds

const getSessionKey = (userId: string): string => `${SESSION_PREFIX}${userId}`;

export const setUserSession = async (
  userId: string,
  session: UserSession
): Promise<void> => {
  try {
    const key = getSessionKey(userId);
    const value = JSON.stringify(session);
    await redisService.set(key, value, SESSION_TTL);
  } catch (error) {
    console.error(`Failed to set session for user ${userId}:`, error);
    throw error;
  }
};

export const getUserSession = async (
  userId: string
): Promise<UserSession | undefined> => {
  try {
    const key = getSessionKey(userId);
    const value = await redisService.get(key);

    if (!value) {
      return undefined;
    }

    return JSON.parse(value) as UserSession;
  } catch (error) {
    console.error(`Failed to get session for user ${userId}:`, error);
    return undefined;
  }
};

export const clearUserSession = async (userId: string): Promise<void> => {
  try {
    const key = getSessionKey(userId);
    await redisService.del(key);
  } catch (error) {
    console.error(`Failed to clear session for user ${userId}:`, error);
    throw error;
  }
};

export const updateUserSession = async (
  userId: string,
  updates: Partial<UserSession>
): Promise<void> => {
  try {
    const currentSession = (await getUserSession(userId)) || {};
    const updatedSession = { ...currentSession, ...updates };
    await setUserSession(userId, updatedSession);
  } catch (error) {
    console.error(`Failed to update session for user ${userId}:`, error);
    throw error;
  }
};

export const clearSessionProperty = async (
  userId: string,
  property: keyof UserSession
): Promise<void> => {
  try {
    const session = await getUserSession(userId);
    if (session) {
      delete session[property];
      if (Object.keys(session).length === 0) {
        await clearUserSession(userId);
      } else {
        await setUserSession(userId, session);
      }
    }
  } catch (error) {
    console.error(
      `Failed to clear session property ${property} for user ${userId}:`,
      error
    );
    throw error;
  }
};
