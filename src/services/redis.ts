import { createClient, RedisClientType } from "redis";
import dotenv from "dotenv";

dotenv.config();

class RedisService {
  private client: RedisClientType | null = null;
  private isConnected = false;

  async connect(): Promise<void> {
    if (this.isConnected && this.client) {
      return;
    }

    try {
      const redisHost =
        process.env.REDIS_HOST ?? process.env.REDIS_HOST_LOCAL ?? "localhost";
      const redisPort =
        process.env.REDIS_PORT ?? process.env.REDIS_PORT_LOCAL ?? "6379";
      const redisUrl = `redis://${redisHost}:${redisPort}`;

      this.client = createClient({
        url: redisUrl,
        socket: {
          reconnectStrategy: (retries) => {
            console.warn(`🔁 Redis reconnect attempt #${retries}`);
            // wait 2 seconds before retrying, limit to 10 retries
            if (retries > 10) return new Error("Too many retries");
            return 2000;
          },
          connectTimeout: 30000,
        },
      });

      this.client.on("error", (err) => {
        // Log Redis configuration details
        console.log("Redis Configuration:");
        console.log("  REDIS_HOST:", process.env.REDIS_HOST);
        console.log("  REDIS_PORT:", process.env.REDIS_PORT);
        console.log("  REDIS_HOST_LOCAL:", process.env.REDIS_HOST_LOCAL);
        console.log("  REDIS_PORT_LOCAL:", process.env.REDIS_PORT_LOCAL);
        console.log("  Resolved redisHost:", redisHost);
        console.log("  Resolved redisPort:", redisPort);
        console.log("  Final Redis URL:", redisUrl);

        console.error("Redis Client Error:", err);
        this.isConnected = false;
      });

      this.client.on("connect", () => {
        console.log("✅ Redis connected");
        this.isConnected = true;
      });

      this.client.on("disconnect", () => {
        console.log("❌ Redis disconnected");
        this.isConnected = false;
      });

      await this.client.connect();
    } catch (error) {
      console.error("Failed to connect to Redis:", error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client && this.isConnected) {
      await this.client.disconnect();
      this.isConnected = false;
      this.client = null;
    }
  }

  private ensureConnected(): void {
    if (!this.client || !this.isConnected) {
      throw new Error("Redis client is not connected");
    }
  }

  async set(key: string, value: string, ttlSeconds?: number): Promise<void> {
    this.ensureConnected();

    if (ttlSeconds) {
      await this.client!.setEx(key, ttlSeconds, value);
    } else {
      await this.client!.set(key, value);
    }
  }

  async get(key: string): Promise<string | null> {
    this.ensureConnected();
    return await this.client!.get(key);
  }

  async del(key: string): Promise<number> {
    this.ensureConnected();
    return await this.client!.del(key);
  }

  async exists(key: string): Promise<number> {
    this.ensureConnected();
    return await this.client!.exists(key);
  }

  async expire(key: string, seconds: number): Promise<number> {
    this.ensureConnected();
    return await this.client!.expire(key, seconds);
  }

  async keys(pattern: string): Promise<string[]> {
    this.ensureConnected();
    return await this.client!.keys(pattern);
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }
}

export const redisService = new RedisService();
