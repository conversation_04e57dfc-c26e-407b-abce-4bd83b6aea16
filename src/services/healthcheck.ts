import { redisService } from "./redis";

export class HealthcheckService {
  private static readonly HEALTHCHECK_KEY = "healthcheck";

  static async writeInitHealthcheck(): Promise<void> {
    try {
      const timestamp = new Date().toISOString();
      await redisService.set(this.HEALTHCHECK_KEY, timestamp);
      console.log(`✅ Bot initialization healthcheck written to Redis: ${timestamp}`);
    } catch (error) {
      console.error("❌ Failed to write healthcheck to Redis:", error);
      throw error;
    }
  }

  static async getLastHealthcheck(): Promise<string | null> {
    try {
      return await redisService.get(this.HEALTHCHECK_KEY);
    } catch (error) {
      console.error("❌ Failed to read healthcheck from Redis:", error);
      return null;
    }
  }

  static async isHealthy(): Promise<boolean> {
    try {
      const lastHealthcheck = await this.getLastHealthcheck();
      if (!lastHealthcheck) {
        return false;
      }

      const lastHealthcheckTime = new Date(lastHealthcheck);
      const now = new Date();
      const timeDiff = now.getTime() - lastHealthcheckTime.getTime();
      const hoursDiff = timeDiff / (1000 * 60 * 60);

      // Consider healthy if last healthcheck was within 24 hours
      return hoursDiff < 24;
    } catch (error) {
      console.error("❌ Failed to check health status:", error);
      return false;
    }
  }
}
