"use client";

import { useEffect, useState } from 'react';
import { CheckCircle, Clock, X } from 'lucide-react';

interface CountdownPopupProps {
  show: boolean;
  onClose: () => void;
  onComplete: () => void;
  initialSeconds?: number;
  title?: string;
  message?: string;
}

export function CountdownPopup({ 
  show, 
  onClose, 
  onComplete, 
  initialSeconds = 60,
  title = "Deposit Processing",
  message = "You will receive your funds within"
}: CountdownPopupProps) {
  const [countdown, setCountdown] = useState(initialSeconds);

  useEffect(() => {
    if (show) {
      setCountdown(initialSeconds);
    }
  }, [show, initialSeconds]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (show && countdown > 0) {
      interval = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else if (countdown === 0 && show) {
      onComplete();
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [show, countdown, onComplete]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (!show) return null;

  return (
    <div className="fixed bottom-4 left-4 right-4 z-[60] max-w-sm mx-auto">
      <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4 animate-in slide-in-from-bottom-2 duration-300">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            <CheckCircle className="w-6 h-6 text-green-600" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-sm font-medium text-gray-900">
                {title}
              </h3>
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Clock className="w-3 h-3" />
                <span className="font-mono">{formatTime(countdown)}</span>
              </div>
            </div>
            <p className="text-sm text-gray-600">
              {message} {formatTime(countdown)} minutes.
            </p>
          </div>
          <button
            onClick={onClose}
            className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close notification"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
