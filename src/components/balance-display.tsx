'use client'

import { UserBalance } from '@/core.constants';

interface BalanceDisplayProps {
  balance?: UserBalance;
  className?: string;
}

export default function BalanceDisplay({ balance, className = '' }: BalanceDisplayProps) {
  const totalBalance = balance ? balance.sum : 0;
  const lockedBalance = balance ? balance.locked : 0;
  const availableBalance = totalBalance - lockedBalance;

  return (
    <div className={`bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-4 ${className}`}>
      <h3 className="text-lg font-semibold mb-3">Your Balance</h3>
      
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-blue-100">Total Balance:</span>
          <span className="text-xl font-bold">{totalBalance.toFixed(2)} TON</span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-blue-100">Available:</span>
          <span className="text-lg font-semibold">{availableBalance.toFixed(2)} TON</span>
        </div>
        
        {lockedBalance > 0 && (
          <div className="flex justify-between items-center">
            <span className="text-blue-100">Locked:</span>
            <span className="text-sm">{lockedBalance.toFixed(2)} TON</span>
          </div>
        )}
      </div>
      
      <div className="mt-3 pt-3 border-t border-blue-400">
        <p className="text-xs text-blue-100">
          Locked funds are reserved for pending orders
        </p>
      </div>
    </div>
  );
}
