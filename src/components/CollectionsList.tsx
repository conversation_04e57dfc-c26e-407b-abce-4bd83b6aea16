"use client";

import { useRootContext } from '@/root-context';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';

export function CollectionsList() {
  const { collections, refreshCollections } = useRootContext();

  const handleRefresh = async () => {
    await refreshCollections();
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Collections</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          className="flex items-center gap-2"
        >
          <RefreshCw className="w-4 h-4" />
          Refresh
        </Button>
      </div>

      {collections.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          No collections found
        </div>
      ) : (
        <div className="grid gap-4">
          {collections.map((collection) => (
            <div
              key={collection.id}
              className="border rounded-lg p-4 space-y-2"
            >
              <div className="flex items-center justify-between">
                <h3 className="font-medium">{collection.name}</h3>
                <span className="text-sm px-2 py-1 bg-gray-100 rounded">
                  {collection.status}
                </span>
              </div>
              <p className="text-sm text-gray-600">{collection.description}</p>
              <div className="text-sm text-gray-500">
                Floor Price: {collection.floorPrice} TON
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
