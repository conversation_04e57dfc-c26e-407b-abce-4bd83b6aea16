"use client";

import { useState, useEffect } from 'react';
import { getUserById } from '@/api/auth-api';
import { UserEntity } from '@/core.constants';

interface UserProfileDisplayProps {
  userId?: string; // Optional - if not provided, uses current user
  className?: string;
}

export default function UserProfileDisplay({ userId, className = '' }: UserProfileDisplayProps) {
  const [user, setUser] = useState<UserEntity | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchUser = async () => {
      try {
        setIsLoading(true);
        setError('');
        
        // getUserById will use current user if userId is not provided
        const userData = await getUserById(userId);
        setUser(userData);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user data';
        setError(errorMessage);
        console.error('Error fetching user:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="bg-gray-200 rounded-lg p-6">
          <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-300 rounded w-1/2 mb-4"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-300 rounded w-full"></div>
            <div className="h-3 bg-gray-300 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <p className="text-red-600 text-sm">Error: {error}</p>
      </div>
    );
  }

  if (!user) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
        <p className="text-gray-600 text-sm">User not found</p>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      <div className="flex items-start space-x-4">
        {/* Avatar placeholder */}
        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
          <span className="text-white font-semibold text-lg">
            {user.name?.charAt(0)?.toUpperCase() || user.id.charAt(0).toUpperCase()}
          </span>
        </div>

        {/* User Info */}
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-gray-900 truncate">
            {user.name || 'Anonymous User'}
          </h3>
          
          <div className="mt-2 space-y-1 text-sm text-gray-600">
            <div className="flex items-center">
              <span className="font-medium">Role:</span>
              <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
                user.role === 'admin' 
                  ? 'bg-red-100 text-red-800' 
                  : 'bg-blue-100 text-blue-800'
              }`}>
                {user.role || 'user'}
              </span>
            </div>

            {user.tg_id && (
              <div className="flex items-center">
                <span className="font-medium">Telegram ID:</span>
                <span className="ml-2 font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                  {user.tg_id}
                </span>
              </div>
            )}

            {user.ton_wallet_address && (
              <div className="flex items-center">
                <span className="font-medium">TON Wallet:</span>
                <span className="ml-2 font-mono text-xs bg-gray-100 px-2 py-1 rounded truncate">
                  {user.ton_wallet_address.slice(0, 8)}...{user.ton_wallet_address.slice(-8)}
                </span>
              </div>
            )}

            {user.balance && (
              <div className="flex items-center">
                <span className="font-medium">Balance:</span>
                <span className="ml-2 text-green-600 font-semibold">
                  {user.balance.sum.toFixed(2)} TON
                </span>
                {user.balance.locked > 0 && (
                  <span className="ml-1 text-orange-600 text-xs">
                    ({user.balance.locked.toFixed(2)} locked)
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
