"use client";

import { UserEntity } from "@/core.constants";
import { Address, beginCell, toNano } from "@ton/ton";
import { useTonConnectUI } from "@tonconnect/ui-react";
import { useCallback, useState } from "react";

interface BalanceTopupProps {
  user: UserEntity;
  onTopupSuccess?: (amount: number) => void;
}

const MIN_TOPUP_AMOUNT = 1; // 1 TON minimum

export default function BalanceTopup({
  user,
  onTopupSuccess,
}: BalanceTopupProps) {
  const [tonConnectUI] = useTonConnectUI();
  const [amount, setAmount] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>("");

  // Check if user can top up
  const canTopup = user.tg_id && user.ton_wallet_address;

  const validateAmount = useCallback((value: string): boolean => {
    const numValue = parseFloat(value);
    return !isNaN(numValue) && numValue >= MIN_TOPUP_AMOUNT;
  }, []);

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAmount(value);
    setError("");

    if (value && !validateAmount(value)) {
      setError(`Minimum amount is ${MIN_TOPUP_AMOUNT} TON`);
    }
  };

  const handleTopup = async () => {
    if (!canTopup) {
      setError("Please connect your Telegram and TON wallet first");
      return;
    }

    if (!validateAmount(amount)) {
      setError(`Minimum amount is ${MIN_TOPUP_AMOUNT} TON`);
      return;
    }

    if (!tonConnectUI.connected) {
      setError("Please connect your TON wallet");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const marketplaceWallet = process.env.NEXT_PUBLIC_TON_MARKETPLACE_WALLET;
      if (!marketplaceWallet) {
        throw new Error("Marketplace wallet not configured");
      }

      const marketplaceAddress = Address.parse(marketplaceWallet);
      const topupAmount = toNano(amount);

      // Create transaction to marketplace wallet
      await tonConnectUI.sendTransaction({
        validUntil: Math.floor(Date.now() / 1000) + 60, // 1 minute
        messages: [
          {
            address: marketplaceAddress.toString(),
            amount: topupAmount.toString(),
            payload: beginCell()
              .storeUint(0, 32) // op code
              .storeStringTail(`Topup:${user.tg_id}`) // comment with user tg_id
              .endCell()
              .toBoc()
              .toString("base64"),
          },
        ],
      });

      // Transaction sent successfully
      onTopupSuccess?.(parseFloat(amount));
      setAmount("");
    } catch (error) {
      console.error("Error sending topup transaction:", error);
      setError(
        error instanceof Error ? error.message : "Failed to send transaction"
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (!canTopup) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-yellow-800 mb-2">
          Complete Your Profile
        </h3>
        <p className="text-yellow-700 mb-3">
          To top up your balance, you need to:
        </p>
        <ul className="list-disc list-inside text-yellow-700 space-y-1">
          {!user.tg_id && <li>Connect your Telegram account</li>}
          {!user.ton_wallet_address && <li>Connect your TON wallet</li>}
        </ul>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Top Up Balance
      </h3>

      <div className="space-y-4">
        <div>
          <label
            htmlFor="amount"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Amount (TON)
          </label>
          <input
            type="number"
            id="amount"
            value={amount}
            onChange={handleAmountChange}
            min={MIN_TOPUP_AMOUNT}
            step="0.1"
            placeholder={`Minimum ${MIN_TOPUP_AMOUNT} TON`}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
          <p className="text-xs text-gray-500 mt-1">
            Minimum amount: {MIN_TOPUP_AMOUNT} TON
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <button
          onClick={handleTopup}
          disabled={isLoading || !amount || !validateAmount(amount)}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? "Processing..." : `Top Up ${amount || "0"} TON`}
        </button>

        <div className="text-xs text-gray-500">
          <p>• Funds will be sent to the marketplace wallet</p>
          <p>• Your balance will be updated automatically after confirmation</p>
          <p>• Transaction fees apply as per TON network</p>
        </div>
      </div>
    </div>
  );
}
