"use client";

import { useLaunchParams } from "@telegram-apps/sdk-react";
import { type PropsWithChildren, useEffect, useState } from "react";

import { ErrorBoundary } from "@/components/ErrorBoundary";
import { ErrorPage } from "@/components/ErrorPage";
import { useDidMount } from "@/hooks/useDidMount";
import { useTelegramTheme } from "@/hooks/useTelegramTheme";
import { handleReferralFromUrl } from "@/utils/referral-utils";
import { AppRoot } from "@telegram-apps/telegram-ui";

function TelegramRootInner({ children }: PropsWithChildren) {
  // This component assumes Telegram SDK is available
  const lp = useLaunchParams();

  // Apply Telegram theme variables after hydration
  useTelegramTheme();

  return (
    <AppRoot
      appearance="dark"
      platform={
        ["macos", "ios"].includes(lp.tgWebAppPlatform) ? "ios" : "base"
      }
    >
      {children}
    </AppRoot>
  );
}

function FallbackRootInner({ children }: PropsWithChildren) {
  // This component is used when Telegram SDK is not available
  // Force dark mode for consistency
  return (
    <AppRoot appearance="dark" platform="base">
      {children}
    </AppRoot>
  );
}

function RootInner({ children }: PropsWithChildren) {
  const [telegramAvailable, setTelegramAvailable] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    // Check if we're on admin or auth pages
    const pathname = window.location.pathname;
    const isAdminOrAuth =
      pathname.startsWith("/admin") || pathname.startsWith("/auth");

    if (isAdminOrAuth && process.env.NODE_ENV === "production") {
      // For admin/auth pages in production, don't use Telegram
      setTelegramAvailable(false);
      setIsLoading(false);
      return;
    }

    // Try to detect if Telegram SDK is available
    try {
      // This is a simple check that doesn't call hooks
      if (typeof window !== "undefined" && window.Telegram?.WebApp) {
        setTelegramAvailable(true);
      } else {
        setTelegramAvailable(false);
      }
    } catch {
      setTelegramAvailable(false);
    }

    setIsLoading(false);
  }, []);

  // Always render the same structure to avoid hydration mismatches
  if (isLoading) {
    return (
      <div
        className="bg-[#17212b] text-[#f5f5f5] min-h-screen flex items-center justify-center"
        suppressHydrationWarning
      >
        Loading...
      </div>
    );
  }

  // Use appropriate component based on Telegram availability
  if (telegramAvailable) {
    return <TelegramRootInner>{children}</TelegramRootInner>;
  } else {
    return <FallbackRootInner>{children}</FallbackRootInner>;
  }
}

export function Root(props: PropsWithChildren) {
  // Unfortunately, Telegram Mini Apps does not allow us to use all features of
  // the Server Side Rendering. That's why we are showing loader on the server
  // side.
  const didMount = useDidMount();

  // Handle referral links when the app loads
  useEffect(() => {
    if (didMount) {
      handleReferralFromUrl();
    }
  }, [didMount]);

  // Always render the same structure to avoid hydration mismatches
  return (
    <ErrorBoundary fallback={ErrorPage}>
      {didMount ? (
        <RootInner {...props} />
      ) : (
        <div
          className="bg-[#17212b] text-[#f5f5f5] min-h-screen flex items-center justify-center"
          suppressHydrationWarning
        >
          Loading
        </div>
      )}
    </ErrorBoundary>
  );
}
