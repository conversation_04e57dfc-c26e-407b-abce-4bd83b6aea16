import TgsViewer from "./TgsViewer";

interface BackyardLottieProps {
  className?: string;
  width?: number;
  height?: number;
  loop?: boolean;
  autoplay?: boolean;
  showControls?: boolean;
}

export default function BackyardLottie({
  className = "",
  width = 200,
  height = 200,
  loop = true,
  autoplay = true,
  showControls = false,
}: BackyardLottieProps) {
  return (
    <TgsViewer
      tgsUrl="/marketplace/Backyard.tgs"
      style={{ width: `${width}px`, height: `${height}px` }}
      className={className}
      autoplay={autoplay}
      loop={loop}
      showControls={showControls}
    />
  );
}
