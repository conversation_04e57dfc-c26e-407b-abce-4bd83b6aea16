"use client";

import { useEffect } from "react";
import { bindThemeParamsCssVars, mountMiniAppSync } from "@telegram-apps/sdk-react";

export function useTelegramTheme() {
  useEffect(() => {
    // Only run on client side and after hydration
    if (typeof window === "undefined") return;

    // Small delay to ensure DOM is ready and hydration is complete
    const timeoutId = setTimeout(() => {
      try {
        if (mountMiniAppSync.isAvailable()) {
          mountMiniAppSync();
          bindThemeParamsCssVars();
        }
      } catch (error) {
        console.warn("Failed to bind Telegram theme variables:", error);
      }
    }, 100);

    return () => clearTimeout(timeoutId);
  }, []);
}
