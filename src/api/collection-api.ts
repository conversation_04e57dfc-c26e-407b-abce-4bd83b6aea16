import { Collection } from "@/core.constants";
import { firestore } from "@/root-context";
import {
  collection,
  deleteDoc,
  doc,
  DocumentSnapshot,
  getDocs,
  limit,
  orderBy,
  query,
  setDoc,
  startAfter,
  updateDoc,
  writeBatch,
} from "firebase/firestore";

const COLLECTION_NAME = "collections";

/**
 * Simple Map-based cache for collections
 *
 * Features:
 * - Caches collections for 5 minutes to reduce Firebase reads
 * - Automatically invalidates cache on create/update/delete operations
 * - Provides getAllCollections() and getCollectionById() for cached access
 * - Use clearCollectionsCache() to manually clear cache if needed
 */
const collectionsCache = new Map<string, Collection>();
let allCollectionsCached = false;
let cacheTimestamp = 0;
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 1 day in milliseconds

// Cache management functions
const isCacheValid = () => {
  return Date.now() - cacheTimestamp < CACHE_DURATION;
};

const clearCache = () => {
  collectionsCache.clear();
  allCollectionsCached = false;
  cacheTimestamp = 0;
};

const updateCache = (collections: Collection[]) => {
  collections.forEach(collection => {
    collectionsCache.set(collection.id, collection);
  });
  cacheTimestamp = Date.now();
};

const getCachedCollection = (id: string): Collection | undefined => {
  if (!isCacheValid()) {
    clearCache();
    return undefined;
  }
  return collectionsCache.get(id);
};

const getAllCachedCollections = (): Collection[] | null => {
  if (!isCacheValid() || !allCollectionsCached) {
    return null;
  }
  return Array.from(collectionsCache.values());
};

// Public function to get all collections with caching
export const getAllCollections = async (): Promise<Collection[]> => {
  const cachedCollections = getAllCachedCollections();
  if (cachedCollections) {
    return cachedCollections.sort((a, b) => a.name.localeCompare(b.name));
  }

  try {
    const snapshot = await getDocs(
      query(collection(firestore, COLLECTION_NAME), orderBy("name", "asc"))
    );

    const collections: Collection[] = [];
    snapshot.forEach((doc) => {
      collections.push({ id: doc.id, ...doc.data() } as Collection);
    });

    updateCache(collections);
    allCollectionsCached = true;

    return collections;
  } catch (error) {
    console.error("Error fetching all collections:", error);
    throw error;
  }
};

// Public function to get a single collection by ID with caching
export const getCollectionById = async (id: string): Promise<Collection | null> => {
  const cachedCollection = getCachedCollection(id);
  if (cachedCollection) {
    return cachedCollection;
  }

  try {
    // First try to get all collections if not cached
    const allCollections = await getAllCollections();
    const foundCollection = allCollections.find(collection => collection.id === id);

    return foundCollection || null;
  } catch (error) {
    console.error("Error fetching collection by ID:", error);
    throw error;
  }
};

export const createCollection = async (collectionData: Collection) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, collectionData.id);
    await setDoc(docRef, {
      ...collectionData,
    });

    // Invalidate cache after creating
    clearCache();

    return collectionData.id;
  } catch (error) {
    console.error("Error creating collection:", error);
    throw error;
  }
};

export const updateCollection = async (
  id: string,
  collectionData: Partial<Collection>
) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await updateDoc(docRef, {
      ...collectionData,
    });

    // Invalidate cache after updating
    clearCache();
  } catch (error) {
    console.error("Error updating collection:", error);
    throw error;
  }
};

export const deleteCollection = async (id: string) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await deleteDoc(docRef);

    // Invalidate cache after deleting
    clearCache();
  } catch (error) {
    console.error("Error deleting collection:", error);
    throw error;
  }
};

export const getCollections = async (
  pageSize: number = 10,
  lastDoc?: DocumentSnapshot
) => {
  try {
    // For simple use cases without pagination, try to return from cache
    if (!lastDoc && pageSize >= 100) {
      const cachedCollections = getAllCachedCollections();
      if (cachedCollections) {
        const limitedCollections = cachedCollections
          .sort((a, b) => a.name.localeCompare(b.name))
          .slice(0, pageSize);

        return {
          collections: limitedCollections,
          lastDoc: undefined,
          hasMore: limitedCollections.length === pageSize && cachedCollections.length > pageSize,
        };
      }
    }

    let q = query(
      collection(firestore, COLLECTION_NAME),
      orderBy("name", "asc"),
      limit(pageSize)
    );

    if (lastDoc) {
      q = query(
        collection(firestore, COLLECTION_NAME),
        orderBy("name", "asc"),
        startAfter(lastDoc),
        limit(pageSize)
      );
    }

    const snapshot = await getDocs(q);
    const collections: Collection[] = [];

    snapshot.forEach((doc) => {
      collections.push({ id: doc.id, ...doc.data() } as Collection);
    });

    // Update cache with fetched collections
    updateCache(collections);

    // If we fetched a large number without pagination, mark all collections as cached
    if (!lastDoc && pageSize >= 100 && collections.length < pageSize) {
      allCollectionsCached = true;
    }

    return {
      collections,
      lastDoc: snapshot.docs[snapshot.docs.length - 1],
      hasMore: snapshot.docs.length === pageSize,
    };
  } catch (error) {
    console.error("Error fetching collections:", error);
    throw error;
  }
};

export const clearAllCollections = async () => {
  try {
    const snapshot = await getDocs(collection(firestore, COLLECTION_NAME));
    const batch = writeBatch(firestore);

    snapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log(`Deleted ${snapshot.docs.length} collections`);

    // Clear cache after bulk delete
    clearCache();
  } catch (error) {
    console.error("Error clearing collections:", error);
    throw error;
  }
};

export const createBulkCollections = async (
  collections: Omit<Collection, "id">[]
) => {
  try {
    const batch = writeBatch(firestore);
    const collectionsCollection = collection(firestore, COLLECTION_NAME);

    collections.forEach((collectionData) => {
      const docRef = doc(collectionsCollection);
      batch.set(docRef, {
        ...collectionData,
      });
    });

    await batch.commit();
    console.log(`Created ${collections.length} collections`);

    // Clear cache after bulk create
    clearCache();
  } catch (error) {
    console.error("Error creating bulk collections:", error);
    throw error;
  }
};

// Public function to manually clear the collections cache
export const clearCollectionsCache = () => {
  clearCache();
};
