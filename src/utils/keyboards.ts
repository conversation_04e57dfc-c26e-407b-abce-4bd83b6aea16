import { Markup } from "telegraf";
import dotenv from "dotenv";
import { BUTTON_TEXTS } from "../constants/messages";

dotenv.config();

const WEB_APP_URL =
  process.env.WEB_APP_URL ?? "https://4d5rqhd0-3000.euw.devtunnels.ms/";

export const createMainKeyboard = () => {
  return Markup.keyboard([
    [
      Markup.button.text(BUTTON_TEXTS.MY_BUY_ORDERS),
      Markup.button.text(BUTTON_TEXTS.MY_SELL_ORDERS),
    ],
    [Markup.button.text(BUTTON_TEXTS.GET_REFERRAL_LINK)],
    [Markup.button.text(BUTTON_TEXTS.CONTACT_SUPPORT)],
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
  ]).resize();
};

export const createMarketplaceInlineKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
  ]);
};

export const createOrderHelpKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
    [Markup.button.callback("📋 Order Help", "order_help")],
  ]);
};

export const createSupportKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
    [Markup.button.callback(BUTTON_TEXTS.CONTACT_SUPPORT, "contact_support")],
  ]);
};

export const createOrderActionsKeyboard = (orderId: string) => {
  return Markup.inlineKeyboard([
    [
      Markup.button.callback(
        "🎁 I'm ready to send gift",
        `complete_${orderId}`
      ),
    ],
    [Markup.button.callback(BUTTON_TEXTS.BACK_TO_ORDERS, "back_to_orders")],
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
  ]);
};

export const createOrderBackKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.callback(BUTTON_TEXTS.BACK_TO_ORDERS, "back_to_orders")],
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
  ]);
};

export const createReferralKeyboard = (referralLink: string) => {
  return Markup.inlineKeyboard([
    [
      Markup.button.url(
        BUTTON_TEXTS.SHARE_LINK,
        `https://t.me/share/url?url=${encodeURIComponent(
          referralLink
        )}&text=${encodeURIComponent(
          "🛍️ Join me on this awesome marketplace! Use my referral link to get started:"
        )}`
      ),
    ],
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
  ]);
};

export const createOrderCompletionKeyboard = (orderId: string) => {
  return Markup.inlineKeyboard([
    [Markup.button.callback(BUTTON_TEXTS.CANCEL, `order_${orderId}`)],
  ]);
};

export const createOrderSuccessKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.callback(BUTTON_TEXTS.VIEW_MY_ORDERS, "back_to_orders")],
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
  ]);
};

export const createOrderErrorKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.callback(BUTTON_TEXTS.VIEW_MY_ORDERS, "back_to_orders")],
    [Markup.button.callback(BUTTON_TEXTS.CONTACT_SUPPORT, "contact_support")],
  ]);
};

export { WEB_APP_URL };
