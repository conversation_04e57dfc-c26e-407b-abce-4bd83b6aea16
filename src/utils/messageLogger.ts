interface MessageInfo {
  type: string;
  details: string;
}

export const getMessageInfo = (message: any): MessageInfo => {
  if ("text" in message) {
    return { type: "📄 Text message", details: message.text };
  } else if ("photo" in message) {
    return {
      type: "📸 Photo message",
      details: `${message.photo.length} sizes, file_ids: ${message.photo
        .map((p: any) => p.file_id)
        .join(", ")}`,
    };
  } else if ("document" in message) {
    return {
      type: "📄 Document",
      details: `${message.document.file_name} - file_id: ${message.document.file_id}`,
    };
  } else if ("video" in message) {
    return { type: "🎥 Video", details: `file_id: ${message.video.file_id}` };
  } else if ("audio" in message) {
    return { type: "🎵 Audio", details: `file_id: ${message.audio.file_id}` };
  } else if ("voice" in message) {
    return {
      type: "🎤 Voice message",
      details: `file_id: ${message.voice.file_id}`,
    };
  } else if ("sticker" in message) {
    return {
      type: "🎭 Sticker",
      details: `file_id: ${message.sticker.file_id}`,
    };
  }

  return {
    type: "❓ Unknown message type",
    details: "No specific details available",
  };
};

export const logMessageDetails = (userId: string, message: any): void => {
  console.log("🎁 Echo Gift Mode - Received message from user:", userId);
  console.log("📝 Message data:", JSON.stringify(message, null, 2));

  const messageInfo = getMessageInfo(message);
  console.log(messageInfo.type + ":", messageInfo.details);
};
