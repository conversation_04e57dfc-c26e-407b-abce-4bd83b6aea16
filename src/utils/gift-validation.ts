import { UniqueGift } from "../middleware/business-message-types";

interface GiftAttribute {
  name: string;
  rarityPermille: number;
}

const URL = "https://cdn.changes.tg/gifts";

const fetchIdToNameMapping = async (): Promise<Record<
  string,
  string
> | null> => {
  try {
    const response = await fetch(`${URL}/id-to-name.json`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return (await response.json()) as Record<string, string>;
  } catch (error) {
    console.error("Error fetching id-to-name mapping from CDN:", error);
    return null;
  }
};

const fetchAttributeData = async (
  collectionName: string,
  attributeType: "models" | "patterns" | "backdrops"
): Promise<GiftAttribute[] | null> => {
  try {
    const response = await fetch(
      `${URL}/${attributeType}/${collectionName}/${attributeType}.json`
    );
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return (await response.json()) as GiftAttribute[];
  } catch (error) {
    console.error(
      `Error fetching ${attributeType} data for collection ${collectionName} from CDN:`,
      error
    );
    return null;
  }
};

const validateAttribute = (
  giftAttributeName: string,
  giftRarityPermille: number,
  validAttributes: GiftAttribute[],
  attributeType: string
): boolean => {
  const matchingAttribute = validAttributes.find(
    (attr) =>
      attr.name === giftAttributeName &&
      attr.rarityPermille === giftRarityPermille
  );

  if (!matchingAttribute) {
    console.error(
      `Invalid ${attributeType}: ${giftAttributeName} with rarity ${giftRarityPermille} not found`
    );
    return false;
  }

  return true;
};

export const validateSentGiftWithOrder = async (
  orderCollectionId: string,
  uniqueGift: UniqueGift
): Promise<boolean> => {
  try {
    // Step 1: Get collection name by orderCollectionId
    const idToNameMapping = await fetchIdToNameMapping();

    if (!idToNameMapping) {
      console.warn(
        "Failed to fetch collection mapping from CDN, skipping gift validation"
      );
      return true; // Skip validation if CDN request failed
    }

    const collectionName = idToNameMapping[orderCollectionId];

    if (!collectionName) {
      console.error(`Collection name not found for ID: ${orderCollectionId}`);
      throw new Error("Invalid gift send");
    }

    // Step 2: Validate model
    const modelsData = await fetchAttributeData(collectionName, "models");
    if (!modelsData) {
      console.warn(
        "Failed to fetch models data from CDN, skipping gift validation"
      );
      return true; // Skip validation if CDN request failed
    }

    const isModelValid = validateAttribute(
      uniqueGift.gift.model.name,
      uniqueGift.gift.model.rarity_per_mille,
      modelsData,
      "model"
    );

    if (!isModelValid) {
      throw new Error("Invalid gift send");
    }

    // Step 3: Validate symbol (patterns)
    const patternsData = await fetchAttributeData(collectionName, "patterns");
    if (!patternsData) {
      console.warn(
        "Failed to fetch patterns data from CDN, skipping gift validation"
      );
      return true; // Skip validation if CDN request failed
    }

    const isSymbolValid = validateAttribute(
      uniqueGift.gift.symbol.name,
      uniqueGift.gift.symbol.rarity_per_mille,
      patternsData,
      "symbol"
    );

    if (!isSymbolValid) {
      throw new Error("Invalid gift send");
    }

    // Step 4: Validate backdrops
    const backdropsData = await fetchAttributeData(collectionName, "backdrops");
    if (!backdropsData) {
      console.warn(
        "Failed to fetch backdrops data from CDN, skipping gift validation"
      );
      return true; // Skip validation if CDN request failed
    }

    const isBackdropValid = validateAttribute(
      uniqueGift.gift.backdrop.name,
      uniqueGift.gift.backdrop.rarity_per_mille,
      backdropsData,
      "backdrop"
    );

    if (!isBackdropValid) {
      throw new Error("Invalid gift send");
    }

    console.log("Backdrop is valid");

    // All validations passed
    return true;
  } catch (error) {
    console.error("Gift validation failed:", error);
    throw error;
  }
};
