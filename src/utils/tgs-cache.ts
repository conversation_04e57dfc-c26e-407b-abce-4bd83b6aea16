interface CachedTgsData {
  lottie<PERSON>son: object;
  timestamp: number;
}

const tgsCache = new Map<string, CachedTgsData>();
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes in milliseconds

// Cache management functions
const isCacheValid = (timestamp: number): boolean => {
  return Date.now() - timestamp < CACHE_DURATION;
};

const clearCache = () => {
  tgsCache.clear();
};

const generateCacheKey = (tgsUrl: string): string => {
  // Use the URL as the cache key
  return tgsUrl;
};

/**
 * Get cached TGS data (Lottie JSON) for a given URL
 */
export const getCachedTgsData = (tgsUrl: string): object | null => {
  const cacheKey = generateCacheKey(tgsUrl);
  const cached = tgsCache.get(cacheKey);

  if (cached && isCacheValid(cached.timestamp)) {
    return cached.lottieJson;
  }

  // Remove expired cache entry
  if (cached) {
    tgsCache.delete(cacheKey);
  }

  return null;
};

/**
 * Set cached TGS data (Lottie JSON) for a given URL
 */
export const setCachedTgsData = (tgsUrl: string, lottieJson: object): void => {
  const cacheKey = generateCacheKey(tgsUrl);
  tgsCache.set(cacheKey, {
    lottieJson,
    timestamp: Date.now()
  });
};

/**
 * Clear all cached TGS data
 */
export const clearTgsCache = (): void => {
  clearCache();
};

/**
 * Get cache statistics for debugging
 */
export const getTgsCacheStats = () => {
  const now = Date.now();
  const validEntries = Array.from(tgsCache.values()).filter(
    entry => isCacheValid(entry.timestamp)
  );

  return {
    totalEntries: tgsCache.size,
    validEntries: validEntries.length,
    expiredEntries: tgsCache.size - validEntries.length,
    cacheSize: tgsCache.size,
    cacheDurationMinutes: CACHE_DURATION / (60 * 1000)
  };
};

/**
 * Clean up expired cache entries
 */
export const cleanupExpiredTgsCache = (): number => {
  let removedCount = 0;

  for (const [key, value] of tgsCache.entries()) {
    if (!isCacheValid(value.timestamp)) {
      tgsCache.delete(key);
      removedCount++;
    }
  }

  return removedCount;
};

// Periodic cleanup of expired cache entries (every 10 minutes)
if (typeof window !== 'undefined') {
  setInterval(() => {
    const removedCount = cleanupExpiredTgsCache();
    if (removedCount > 0) {
      console.log(`TGS Cache: Cleaned up ${removedCount} expired entries`);
    }
  }, 10 * 60 * 1000); // 10 minutes
}

// Development helper: expose cache functions to window for debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).tgsCache = {
    getStats: getTgsCacheStats,
    clear: clearTgsCache,
    cleanup: cleanupExpiredTgsCache,
    size: () => tgsCache.size
  };
}
