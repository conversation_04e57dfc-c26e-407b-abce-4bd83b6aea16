[{"_": "starGiftAttributeModel", "name": "Asterix", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "RGB Glitch", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Artwork", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Cartoon", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Fun Time", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Honey Bee", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Snowfall", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Captain", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Cotton Candy", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Falcon", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Toxic Guy", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Neon", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Voltage", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Tron", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Redrum", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Bog <PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Duck Tales", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON><PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Sunrise", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Autumn", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Sea Sunset", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Seabreeze", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Nightshade", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Apple Slice", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Chicago Bulls", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Freshwave", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Pink Pop", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Night Ivy", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Negative", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Creamsicle", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Bluebird", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Shadow", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Ivory", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Villager", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Aurora", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Sky High", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Sepium", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Patriot", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Macintosh", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Duskwave", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Bordeaux", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Pokemon", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Classic", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Frost<PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 20}]