[{"_": "starGiftAttributeModel", "name": "The Seeker", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Wednesday", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Broken Heart", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Druid Circle", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Disco", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Femme Fatale", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Honey Cake", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "The Lich King", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Sanguine", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Original Sin", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "World Cup", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Incubus", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Billiard", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Neb<PERSON>", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Oculus", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON><PERSON>", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Poor Kitty", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Pink Panther", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Electric Shock", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Rainbow", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Opacity", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Fortune", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Spice Storm", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Vitamin C", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Cartoon", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON><PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Inkwell", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Hot Head", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Crash Test", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Pink Chess", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Warhol", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Frankenstein", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Past Tell", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Death Wish", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Futuristic", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Silver", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON><PERSON>", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Ghosted", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Deathquik", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Brain Freeze", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>r", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Refreshing", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Seeing Red", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Peridot", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Martian", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Red Skull", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Lady Death", "rarityPermille": 30}]