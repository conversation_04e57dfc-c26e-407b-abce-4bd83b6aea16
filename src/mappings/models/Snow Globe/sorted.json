[{"_": "starGiftAttributeModel", "name": "Musk", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON><PERSON>", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Bag End", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Party Cat", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON><PERSON>", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "House of Cards", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Fish Tank", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Ginger House", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Piggy Bank", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Secret Gift", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Baobab", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Autumn", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Dracula", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Summer", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Crimson Cabin", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Nether House", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Bedrock", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Bronze Temple", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Winter", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Spring", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Silver Frost", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Ender Estate", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Wild West", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Bikini Bottom", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Happy Town", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Pyramid", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Minehouse", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Apocalypse", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Orange Tale", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Sky Terrace", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Diamond", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Lost Color", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Meadow", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Sea Whisper", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Ocean Oasis", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Mango Lodge", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Inversion", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Caramel", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Green Villa", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Choco Waffle", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Lavender", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON><PERSON>", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Emerald", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Night Snow", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Amethyst", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Pink Dreams", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Midas Manor", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "White Day", "rarityPermille": 30}]