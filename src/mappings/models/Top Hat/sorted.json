[{"_": "starGiftAttributeModel", "name": "Pixel Perfect", "rarityPermille": 2}, {"_": "starGiftAttributeModel", "name": "Megawatt", "rarityPermille": 3}, {"_": "starGiftAttributeModel", "name": "Constable", "rarityPermille": 3}, {"_": "starGiftAttributeModel", "name": "Hypnosis", "rarityPermille": 3}, {"_": "starGiftAttributeModel", "name": "Lucifer", "rarityPermille": 3}, {"_": "starGiftAttributeModel", "name": "Solar System", "rarityPermille": 3}, {"_": "starGiftAttributeModel", "name": "Grey Gatsby", "rarityPermille": 3}, {"_": "starGiftAttributeModel", "name": "Polygon", "rarityPermille": 3}, {"_": "starGiftAttributeModel", "name": "Abracadabra", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Tree Stump", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Edison", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Prestige", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Green Stars", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Romantic", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Sceleritas Fel", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Is It Cake?", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Alien Invasion", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Mr. <PERSON>", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Hippie", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Bubble Top", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Snowstorm", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Atlantis", "rarityPermille": 5}, {"_": "starGiftAttributeModel", "name": "Celestial", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Performer", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Witch Doctor", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Glam Safari", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Blackjack", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Steampunk", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Antiquity", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "<PERSON> Bunny", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Submarine", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON>", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 10}, {"_": "starGiftAttributeModel", "name": "Chronos", "rarityPermille": 12}, {"_": "starGiftAttributeModel", "name": "Huntsman", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Silver Glass", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Elegance", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Uncle <PERSON>", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Cranberry", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 15}, {"_": "starGiftAttributeModel", "name": "Lincoln", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Burned Brass", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Crosswalk", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Limoncello", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Scrooge", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Clockwork", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Blood Veil", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Low Rider", "rarityPermille": 20}, {"_": "starGiftAttributeModel", "name": "Starlight", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Grandmaster", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Spectrum", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Dreamwave", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Purple Rain", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Victorian Era", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Golden Ratio", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Bloodline", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "<PERSON><PERSON><PERSON><PERSON>", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "<PERSON>", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Alabaster", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Maroon", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "White Windsor", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Black Raven", "rarityPermille": 30}, {"_": "starGiftAttributeModel", "name": "Maestro", "rarityPermille": 30}]