rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {

    // Helper function to check if a request is from an admin
    function isAdmin() {
      let userDoc = firestore.get(/databases/(default)/documents/users/$(request.auth.uid));
      return request.auth != null && userDoc != null && userDoc.data.role == 'admin';
    }
  
    // Allow authenticated users to read their own avatars
    match /{allPaths=**} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }

  }
}