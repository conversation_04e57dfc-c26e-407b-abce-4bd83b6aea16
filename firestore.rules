rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper function to check if a request is from an admin
    function isAdmin() {
      let userDoc = /databases/$(database)/documents/users/$(request.auth.uid);
      return request.auth != null && exists(userDoc) && get(userDoc).data.role == 'admin';
    }

    // Rules for the 'users' collection
    match /users/{userId} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }

    // Rules for the 'apps' collection
    match /collections/{document=**} {
      allow read: if true; // Everyone can view
      allow create, update, delete: if isAdmin();
    }
  }
}