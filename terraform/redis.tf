# Use default VPC for Redis (no custom VPC needed)
data "google_compute_network" "default" {
  name = "default"
}

# Create Redis instance using default VPC
resource "google_redis_instance" "cache" {
  name           = "${var.service_name}-redis"
  tier           = "STANDARD_HA"
  memory_size_gb = 1
  region         = var.region
  redis_version  = "REDIS_5_0"
  
  # Security enhancements
  auth_enabled            = false
  transit_encryption_mode = "DISABLED"
  
  # Use default VPC network
  authorized_network = data.google_compute_network.default.id
  
  # Maintenance window
  maintenance_policy {
    weekly_maintenance_window {
      day = "SUNDAY"
      start_time {
        hours   = 2
        minutes = 0
      }
    }
  }
  
  depends_on = [google_project_service.required_apis]
}
