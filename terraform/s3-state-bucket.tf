resource "google_storage_bucket" "terraform_state" {
  name          = "${var.project_id}-terraform-state"
  location      = "US"
  force_destroy = false

  # Enable versioning to keep state history
  versioning {
    enabled = true
  }

  # Prevent public access
  uniform_bucket_level_access = true

  # Lifecycle management to control costs
  lifecycle_rule {
    condition {
      age = 1825
    }
    action {
      type = "Delete"
    }
  }

  # Prevent accidental deletion
  lifecycle {
    prevent_destroy = true
  }
}