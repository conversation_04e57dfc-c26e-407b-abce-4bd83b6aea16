# Cloud Run service with Direct VPC egress
resource "google_cloud_run_service" "nodejs_app" {
  name     = var.service_name
  location = var.region

  template {
    spec {
      containers {
        image = "${var.region}-docker.pkg.dev/${var.project_id}/${var.service_name}/${var.service_name}:latest"
        
        ports {
          container_port = 8080
        }

        resources {
          limits = {
            cpu    = "1000m"
            memory = "512Mi"
          }
        }

        startup_probe {
          tcp_socket {
            port = 8080
          }
          initial_delay_seconds = 5
          period_seconds = 30        # Check every 30 seconds (less frequent)
          failure_threshold = 10     # Allow more failures before restart
          timeout_seconds = 1
        }

        # startup_probe {
        #   http_get {
        #     path = "/healthcheck"
        #     port = 8081
        #   }
        #   initial_delay_seconds = 60
        #   timeout_seconds = 15
        #   period_seconds = 15
        #   failure_threshold = 5
        # }

        env {
          name  = "NODE_ENV"
          value = "production"
        }
        
        env {
          name  = "REDIS_HOST"
          value = google_redis_instance.cache.host
        }
        
        env {
          name  = "REDIS_PORT"
          value = tostring(google_redis_instance.cache.port)
        }
        
        env {
          name  = "FIREBASE_FUNCTIONS_URL"
          value = "https://${var.region}-${var.project_id}.cloudfunctions.net"
        }
      }

      timeout_seconds = 60

      service_account_name = google_service_account.cloud_run_service_sa.email
    }

    metadata {
      annotations = {
        "autoscaling.knative.dev/maxScale"      = "10"
        "run.googleapis.com/cpu-throttling"     = "false"
        # Enable Direct VPC egress for Redis connectivity[1]
        "run.googleapis.com/vpc-access-egress"  = "private-ranges-only"
        "run.googleapis.com/network-interfaces" = jsonencode([{
          network    = data.google_compute_network.default.id
          subnetwork = "projects/${var.project_id}/regions/${var.region}/subnetworks/default"
        }])
      }
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }

  depends_on = [
    google_project_service.required_apis,
    google_redis_instance.cache
  ]
}

# IAM policy for public access
resource "google_cloud_run_service_iam_member" "public_access" {
  location = google_cloud_run_service.nodejs_app.location
  project  = google_cloud_run_service.nodejs_app.project
  service  = google_cloud_run_service.nodejs_app.name
  role     = "roles/run.invoker"
  member   = "allUsers"
}
