# Service account for Terraform operations
resource "google_service_account" "terraform_sa" {
  account_id   = "terraform-sa"
  display_name = "Terraform Service Account"
  description  = "Service account for Terraform to manage all resources"
}

# Comprehensive roles for Terraform service account
resource "google_project_iam_member" "terraform_roles" {
  for_each = toset([
    "roles/run.admin",
    "roles/iam.serviceAccountAdmin",
    "roles/iam.serviceAccountUser",
    "roles/artifactregistry.admin",
    "roles/serviceusage.serviceUsageAdmin",
    "roles/resourcemanager.projectIamAdmin",
    "roles/cloudbuild.builds.builder",
    "roles/redis.admin",
    "roles/compute.networkViewer"
  ])

  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.terraform_sa.email}"
}

# Service account for Cloud Run service
resource "google_service_account" "cloud_run_service_sa" {
  account_id   = "cloud-run-service-sa"
  display_name = "Cloud Run Service Account"
  description  = "Service account for the Cloud Run service runtime"
}

# Grant minimal permissions to Cloud Run service account
resource "google_project_iam_member" "cloud_run_service_roles" {
  for_each = toset([
    "roles/logging.logWriter",
    "roles/monitoring.metricWriter",
    "roles/cloudtrace.agent",
    "roles/redis.editor"
  ])

  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.cloud_run_service_sa.email}"
}

# Service account for GitHub Actions deployment
resource "google_service_account" "github_actions_sa" {
  account_id   = "github-actions-sa"
  display_name = "GitHub Actions Deployment SA"
  description  = "Service account for GitHub Actions to deploy to Cloud Run"
}

# Grant deployment permissions to GitHub Actions service account
resource "google_project_iam_member" "github_actions_roles" {
  for_each = toset([
    "roles/run.developer",
    "roles/artifactregistry.writer",
    "roles/iam.serviceAccountUser",
    "roles/serviceusage.serviceUsageConsumer"
  ])

  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.github_actions_sa.email}"
}

# Allow GitHub Actions SA to act as Cloud Run service account
resource "google_service_account_iam_member" "github_actions_impersonate" {
  service_account_id = google_service_account.cloud_run_service_sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${google_service_account.github_actions_sa.email}"
}

# Generate keys for service accounts
resource "google_service_account_key" "terraform_sa_key" {
  service_account_id = google_service_account.terraform_sa.name
}

resource "google_service_account_key" "github_actions_sa_key" {
  service_account_id = google_service_account.github_actions_sa.name
}
