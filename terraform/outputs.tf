output "service_url" {
  description = "The URL of the deployed Cloud Run service"
  value       = google_cloud_run_service.nodejs_app.status[0].url
}

output "redis_host" {
  description = "Redis instance host"
  value       = google_redis_instance.cache.host
  sensitive   = true
}

output "redis_auth_string" {
  description = "Redis AUTH string"
  value       = google_redis_instance.cache.auth_string
  sensitive   = true
}

output "artifact_registry_url" {
  description = "Artifact Registry repository URL"
  value       = "${var.region}-docker.pkg.dev/${var.project_id}/${var.service_name}"
}

output "terraform_sa_key" {
  description = "Terraform service account key"
  value       = base64decode(google_service_account_key.terraform_sa_key.private_key)
  sensitive   = true
}

output "github_actions_sa_key" {
  description = "GitHub Actions service account key"
  value       = base64decode(google_service_account_key.github_actions_sa_key.private_key)
  sensitive   = true
}

output "terraform_state_bucket" {
  description = "GCS bucket name for Terraform state"
  value       = google_storage_bucket.terraform_state.name
}
