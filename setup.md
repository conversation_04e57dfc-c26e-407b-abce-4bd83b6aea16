# Quick Setup Guide

## 1. Get Your Bot Token

1. Open Telegram and search for [@Bot<PERSON>ather](https://t.me/BotFather)
2. Send `/newbot` command
3. Choose a name for your bot (e.g., "Marketplace Bot")
4. Choose a username for your bot (must end with "bot", e.g., "marketplace_helper_bot")
5. Copy the bot token that BotFather gives you

## 2. Configure Environment

1. Copy the example environment file:

   ```bash
   cp .env.example .env
   ```

2. Edit `.env` file and add your bot token:
   ```env
   BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
   WEB_APP_URL=https://4d5rqhd0-3000.euw.devtunnels.ms/
   ```

## 4. Start the Bot

### Development Mode (with hot reloading):

```bash
npm run dev
```

### Production Mode:

```bash
npm run build
npm start
```

## 5. Test Your Bot

1. Open Telegram
2. Search for your bot by username
3. Send `/start` command
4. Test the buttons:
   - Menu button (should open the web app)
   - "Hello World" button
   - "Complete Order" button

## 6. Optional: Set Menu Button via BotFather

You can also configure the menu button through BotFather:

1. Send `/setmenubutton` to @BotFather
2. Select your bot
3. Choose "Edit menu button"
4. Enter button text: "Open Marketplace"
5. Enter web app URL: `https://4d5rqhd0-3000.euw.devtunnels.ms/`

## Troubleshooting

### Bot not responding

- Check if BOT_TOKEN is correct
- Make sure the bot is not running elsewhere
- Check console for error messages

### Menu button not working

- Verify WEB_APP_URL is accessible
- Check if the URL opens correctly in a browser
- Make sure the URL uses HTTPS (required for Telegram Web Apps)

### Build errors

- Run `npm install` to ensure all dependencies are installed
- Check Node.js version (requires v16+)
- Check TypeScript compilation with `npm run build`
