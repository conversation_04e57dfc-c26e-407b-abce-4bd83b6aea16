#!/bin/bash

echo "🔍 Looking for running bot processes..."

# Find and kill any node processes running the bot
PIDS=$(ps aux | grep -E "(npm.*dev|node.*bot|node.*index)" | grep -v grep | awk '{print $2}')

if [ -z "$PIDS" ]; then
    echo "✅ No bot processes found running"
else
    echo "🛑 Found bot processes with PIDs: $PIDS"
    echo "💀 Killing processes..."
    
    for PID in $PIDS; do
        kill -TERM $PID 2>/dev/null
        echo "   Sent TERM signal to PID $PID"
    done
    
    # Wait a moment for graceful shutdown
    sleep 2
    
    # Force kill if still running
    for PID in $PIDS; do
        if kill -0 $PID 2>/dev/null; then
            kill -KILL $PID 2>/dev/null
            echo "   Force killed PID $PID"
        fi
    done
    
    echo "✅ All bot processes stopped"
fi

echo "🚀 You can now start the bot again with: npm run dev"
