import * as admin from "firebase-admin";
import { TxLookup } from "./types";

const TX_LOOKUP_COLLECTION = "tx_lookup";
const TX_LOOKUP_DOC_ID = "main";

export async function getTxLookup(): Promise<TxLookup | null> {
  try {
    const db = admin.firestore();
    const doc = await db
      .collection(TX_LOOKUP_COLLECTION)
      .doc(TX_LOOKUP_DOC_ID)
      .get();

    if (!doc.exists) {
      return null;
    }

    return {
      id: doc.id,
      ...doc.data(),
    } as TxLookup;
  } catch (error) {
    console.error("Error getting tx lookup:", error);
    throw error;
  }
}

export async function updateTxLookup(
  lastCheckedRecordId: string
): Promise<void> {
  try {
    const db = admin.firestore();
    await db.collection(TX_LOOKUP_COLLECTION).doc(TX_LOOKUP_DOC_ID).set(
      {
        last_checked_record_id: lastCheckedRecordId,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      },
      { merge: true }
    );

    console.log(
      `Updated tx lookup with last_checked_record_id: ${lastCheckedRecordId}`
    );
  } catch (error) {
    console.error("Error updating tx lookup:", error);
    throw error;
  }
}

export async function initializeTxLookup(): Promise<void> {
  try {
    const existing = await getTxLookup();

    if (!existing) {
      const db = admin.firestore();
      await db.collection(TX_LOOKUP_COLLECTION).doc(TX_LOOKUP_DOC_ID).set({
        last_checked_record_id: "0",
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      console.log("Initialized tx lookup record");
    }
  } catch (error) {
    console.error("Error initializing tx lookup:", error);
    throw error;
  }
}
