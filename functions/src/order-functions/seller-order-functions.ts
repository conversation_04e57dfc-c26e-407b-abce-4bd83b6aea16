import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { OrderEntity, OrderStatus } from "../types";
import { hasAvailableBalance, lockFunds } from "../balance-service";
import { getAppConfig } from "../fee-service";
import { getNextCounterValue } from "../counter-service";
import { DEFAULT_SELLER_LOCK_PERCENTAGE } from "../constants";

export const createOrderAsSeller = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { sellerId, productId, collectionId, amount, owned_gift_id } = data;

    if (!sellerId || !productId || !collectionId || !amount || amount <= 0) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "sellerId, productId, collectionId, and valid amount are required."
      );
    }

    if (context.auth.uid !== sellerId) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "You can only create orders for yourself as seller."
      );
    }

    try {
      const db = admin.firestore();

      // Get collection and validate floor price
      const collectionDoc = await db
        .collection("collections")
        .doc(collectionId)
        .get();
      if (!collectionDoc.exists) {
        throw new functions.https.HttpsError(
          "not-found",
          "Collection not found."
        );
      }

      const collection = {
        id: collectionDoc.id,
        ...collectionDoc.data(),
      } as any;
      if (collection.floorPrice && amount < collection.floorPrice) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          `Order amount ${amount} TON is below collection floor price of ${collection.floorPrice} TON.`
        );
      }

      // Get seller lock percentage from app config
      const config = await getAppConfig();
      const sellerLockPercentage =
        config?.seller_lock_percentage ?? DEFAULT_SELLER_LOCK_PERCENTAGE;
      const lockedAmount = amount * sellerLockPercentage;

      // Check if seller has sufficient balance
      const hasBalance = await hasAvailableBalance(sellerId, lockedAmount);
      if (!hasBalance) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          `Insufficient balance. You need at least ${lockedAmount} TON available (${
            sellerLockPercentage * 100
          }% of ${amount} TON order amount).`
        );
      }

      // Lock seller percentage of order amount for seller
      await lockFunds(sellerId, lockedAmount);

      // Get next order number
      const orderNumber = await getNextCounterValue("order_number");

      const orderData: Omit<OrderEntity, "id"> = {
        number: orderNumber,
        sellerId,
        productId,
        collectionId,
        amount,
        status: OrderStatus.ACTIVE,
        owned_gift_id,
        createdAt: admin.firestore.FieldValue.serverTimestamp() as any,
        updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
      };

      const orderRef = await db.collection("orders").add(orderData);

      return {
        success: true,
        orderId: orderRef.id,
        message: `Order created successfully with ${lockedAmount} TON locked (${
          sellerLockPercentage * 100
        }% of ${amount} TON order)`,
      };
    } catch (error) {
      console.error("Error creating order as seller:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while creating order."
      );
    }
  }
);

export const makePurchaseAsSeller = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { sellerId, orderId } = data;

    if (!sellerId || !orderId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "sellerId and orderId are required."
      );
    }

    if (context.auth.uid !== sellerId) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "You can only make purchases for yourself."
      );
    }

    try {
      const db = admin.firestore();

      // Get order
      const orderDoc = await db.collection("orders").doc(orderId).get();
      if (!orderDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Order not found.");
      }

      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Check if order is available for purchase
      if (order.status !== OrderStatus.ACTIVE) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Order is not available for purchase."
        );
      }

      // Check if order already has a seller
      if (order.sellerId && order.sellerId !== sellerId) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Order already has a seller."
        );
      }

      // Check if seller is trying to sell to their own order
      if (order.buyerId === sellerId) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "You cannot sell to your own order."
        );
      }

      // Get seller lock percentage from app config
      const config = await getAppConfig();
      const sellerLockPercentage =
        config?.seller_lock_percentage ?? DEFAULT_SELLER_LOCK_PERCENTAGE;
      const lockedAmount = order.amount * sellerLockPercentage;

      // Check if seller has sufficient balance
      const hasBalance = await hasAvailableBalance(sellerId, lockedAmount);
      if (!hasBalance) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          `Insufficient balance. You need at least ${lockedAmount} TON available (${
            sellerLockPercentage * 100
          }% of ${order.amount} TON order amount).`
        );
      }

      // Lock seller percentage of order amount
      await lockFunds(sellerId, lockedAmount);

      // Update order with seller and change status to paid
      await db.collection("orders").doc(orderId).update({
        sellerId,
        status: OrderStatus.PAID,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      return {
        success: true,
        message: `Purchase successful! ${lockedAmount} TON locked (${
          sellerLockPercentage * 100
        }% of ${order.amount} TON order). You can now send the gift.`,
        lockedAmount,
        orderAmount: order.amount,
        lockPercentage: sellerLockPercentage * 100,
      };
    } catch (error) {
      console.error("Error making purchase as seller:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while making purchase."
      );
    }
  }
);
