import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import { UserEntity } from "./types";
import { getTelegramBotToken, isDevelopment } from "./config";

import * as CryptoJS from "crypto-js";

export interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  photo_url?: string;
}

export interface ValidationResult {
  isValid: boolean;
  user?: TelegramUser;
  error?: string;
}

export function validateTelegramWebAppData(
  initData: string,
  botToken: string
): ValidationResult {
  try {
    // Parse the init data
    const urlParams = new URLSearchParams(initData);
    const hash = urlParams.get("hash");

    if (!hash) {
      return { isValid: false, error: "Hash is missing" };
    }

    // Remove hash from params for validation
    urlParams.delete("hash");

    // Sort parameters alphabetically and create data check string
    const dataCheckArray: string[] = [];
    for (const [key, value] of Array.from(urlParams.entries()).sort()) {
      dataCheckArray.push(`${key}=${value}`);
    }
    const dataCheckString = dataCheckArray.join("\n");

    // Create secret key using bot token
    const secretKey = CryptoJS.HmacSHA256(botToken, "WebAppData");

    // Calculate expected hash
    const expectedHash = CryptoJS.HmacSHA256(
      dataCheckString,
      secretKey
    ).toString();

    // Verify hash
    if (hash !== expectedHash) {
      return { isValid: false, error: "Invalid hash" };
    }

    // Check auth_date (should not be older than 24 hours)
    const authDate = urlParams.get("auth_date");
    if (!authDate) {
      return { isValid: false, error: "Auth date is missing" };
    }

    const authTimestamp = parseInt(authDate, 10);
    const currentTimestamp = Math.floor(Date.now() / 1000);
    const maxAge = 24 * 60 * 60; // 24 hours in seconds

    if (currentTimestamp - authTimestamp > maxAge) {
      return { isValid: false, error: "Auth data is too old" };
    }

    // Parse user data
    const userParam = urlParams.get("user");
    if (!userParam) {
      return { isValid: false, error: "User data is missing" };
    }

    const user: TelegramUser = JSON.parse(userParam);

    return { isValid: true, user };
  } catch (error) {
    return {
      isValid: false,
      error: `Validation error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

export const authenticateWithTelegram = functions.https.onCall(async (data) => {
  const isDevMode = isDevelopment();

  const { initData } = data;

  if (!initData) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "initData is required"
    );
  }

  console.log("[authenticateWithTelegram] Received initData:", initData);
  console.log("[authenticateWithTelegram] Development mode:", isDevMode);

  // Get bot token from Firebase config
  const botToken = getTelegramBotToken();
  if (!botToken) {
    throw new functions.https.HttpsError(
      "failed-precondition",
      "Telegram bot token not configured"
    );
  }

  const db = admin.firestore();

  try {
    let telegramUser: any;
    let telegramId: string;

    // In development mode, allow mock data for testing
    if (isDevMode && initData.includes("mock_hash_for_development")) {
      console.log(
        "[authenticateWithTelegram] Using development mode with mock data"
      );

      const urlParams = new URLSearchParams(initData);
      const userParam = urlParams.get("user");

      if (!userParam) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "User data is missing in mock initData"
        );
      }

      telegramUser = JSON.parse(userParam);
      telegramId = telegramUser.id.toString();

      console.log("[authenticateWithTelegram] Mock user:", telegramUser);
    } else {
      // Production mode: validate Telegram data properly
      console.log("[authenticateWithTelegram] Validating Telegram data...");
      const validation = validateTelegramWebAppData(initData, botToken);

      console.log("[authenticateWithTelegram] Validation result:", {
        isValid: validation.isValid,
        error: validation.error,
        hasUser: !!validation.user,
      });

      if (!validation.isValid || !validation.user) {
        throw new functions.https.HttpsError(
          "permission-denied",
          validation.error || "Invalid Telegram data"
        );
      }

      telegramUser = validation.user;
      telegramId = telegramUser.id.toString();
    }

    console.log("[authenticateWithTelegram] Final user data:", {
      telegramId,
      firstName: telegramUser.first_name,
      username: telegramUser.username,
    });

    // Check if user already exists
    const existingUserQuery = await db
      .collection("users")
      .where("tg_id", "==", telegramId)
      .limit(1)
      .get();

    let userId: string;
    let userRecord: UserEntity;

    if (!existingUserQuery.empty) {
      // User exists, get their data
      const existingUserDoc = existingUserQuery.docs[0];
      userId = existingUserDoc.id;
      userRecord = existingUserDoc.data() as UserEntity;
    } else {
      // Create new user
      const newUserRef = db.collection("users").doc();
      userId = newUserRef.id;

      userRecord = {
        id: userId,
        email: null,
        displayName:
          telegramUser.first_name +
          (telegramUser.last_name ? ` ${telegramUser.last_name}` : ""),
        photoURL: telegramUser.photo_url ?? null,
        role: "user",
        tg_id: telegramId,
      };

      await newUserRef.set(userRecord);
      console.log(
        `New Telegram user created: ${userId} (TG ID: ${telegramId})`
      );
    }

    // Create custom token for Firebase Auth
    console.log(
      "[authenticateWithTelegram] Creating custom token for user:",
      userId
    );

    try {
      // Try to create custom token with additional claims
      const customClaims = {
        tg_id: telegramId,
        provider: "telegram",
        telegram_user: {
          id: telegramUser.id,
          first_name: telegramUser.first_name,
          last_name: telegramUser.last_name,
          username: telegramUser.username,
        },
      };

      const customToken = await admin
        .auth()
        .createCustomToken(userId, customClaims);

      console.log(
        "[authenticateWithTelegram] Custom token created successfully"
      );

      return {
        customToken,
        user: userRecord,
      };
    } catch (tokenError) {
      console.error(
        "[authenticateWithTelegram] Error creating custom token:",
        tokenError
      );
      console.error("[authenticateWithTelegram] Token error details:", {
        code: (tokenError as any)?.code,
        message: (tokenError as any)?.message,
        errorInfo: (tokenError as any)?.errorInfo,
      });

      // Check if this is the IAM permission error
      if (
        (tokenError as any)?.errorInfo?.code === "auth/insufficient-permission"
      ) {
        // Alternative approach: Return user data and let client handle auth differently
        console.log(
          "[authenticateWithTelegram] IAM permission error - using alternative approach"
        );

        // Set custom claims on the user record instead
        try {
          await admin.auth().setCustomUserClaims(userId, {
            tg_id: telegramId,
            provider: "telegram",
            telegram_verified: true,
          });

          console.log(
            "[authenticateWithTelegram] Custom claims set successfully"
          );

          // Return a special response that indicates alternative auth
          return {
            alternativeAuth: true,
            userId: userId,
            user: userRecord,
            message: "Please use Firebase Auth UI to complete sign-in",
          };
        } catch (claimsError) {
          console.error(
            "[authenticateWithTelegram] Error setting custom claims:",
            claimsError
          );
        }
      }

      throw new functions.https.HttpsError(
        "internal",
        `Failed to create authentication token: ${
          tokenError instanceof Error ? tokenError.message : "Unknown error"
        }`
      );
    }
  } catch (error) {
    console.error("Telegram authentication error:", error);

    if (error instanceof functions.https.HttpsError) {
      throw error;
    }

    throw new functions.https.HttpsError("internal", "Authentication failed");
  }
});
