import { getTelegramBotToken } from "./config";

export function verifyBotToken(providedToken: string): boolean {
  try {
    const expectedToken = getTelegramBotToken();

    if (!providedToken || !expectedToken) {
      return false;
    }

    return constantTimeCompare(providedToken, expectedToken);
  } catch (error) {
    console.error("Error verifying bot token:", error);
    return false;
  }
}

function constantTimeCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }

  return result === 0;
}

export function extractBotTokenFromHeader(
  authHeader: string | undefined
): string | null {
  if (!authHeader) {
    return null;
  }

  const parts = authHeader.split(" ");
  if (parts.length !== 2 || parts[0] !== "Bot") {
    return null;
  }

  return parts[1];
}
