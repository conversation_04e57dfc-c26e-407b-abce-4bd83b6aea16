import * as functions from "firebase-functions";
import { getUserBalance } from "./balance-service";

export const getBalance = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required to get balance."
    );
  }

  try {
    const balance = await getUserBalance(context.auth.uid);
    return {
      success: true,
      balance,
    };
  } catch (error) {
    console.error("Error getting user balance:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while getting balance."
    );
  }
});
